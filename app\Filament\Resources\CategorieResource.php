<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CategorieResource\Pages;
use App\Filament\Traits\HandlesImageStorage;
use App\Models\Categorie;
use Filament\Forms;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CategorieResource extends Resource
{
    use HandlesImageStorage;
    protected static ?string $model = Categorie::class;

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    protected static ?string $navigationGroup = 'Catalogue';

    protected static ?int $navigationSort = 1;

    protected static ?string $recordTitleAttribute = 'nom';

    protected static ?string $recordRouteKeyName = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informations de la catégorie')
                            ->schema([
                                Tabs::make('Traductions')
                                    ->tabs([
                                        Tabs\Tab::make('Français')
                                            ->icon('heroicon-m-flag')
                                            ->schema([
                                                Forms\Components\TextInput::make('nom.fr')
                                                    ->label('Nom (FR)')
                                                    ->required()
                                                    ->maxLength(255)
                                                    ->live(onBlur: true)
                                                    ->afterStateUpdated(function (string $operation, $state, Forms\Set $set) {
                                                        if ($operation === 'create' || $operation === 'edit') {
                                                            $set('slug', \Illuminate\Support\Str::slug($state));
                                                        }
                                                    }),

                                                Forms\Components\Textarea::make('description.fr')
                                                    ->label('Description (FR)')
                                                    ->rows(3),
                                            ]),

                                        Tabs\Tab::make('English')
                                            ->icon('heroicon-m-flag')
                                            ->schema([
                                                Forms\Components\TextInput::make('nom.en')
                                                    ->label('Nom (EN)')
                                                    ->maxLength(255),

                                                Forms\Components\Textarea::make('description.en')
                                                    ->label('Description (EN)')
                                                    ->rows(3),
                                            ]),
                                    ])
                                    ->columnSpanFull(),

                                Forms\Components\TextInput::make('slug')
                                    ->label('Slug')
                                    ->required()
                                    ->maxLength(255)
                                    ->unique(ignoreRecord: true)
                                    ->dehydrated(fn ($state) => filled($state))
                                    ->afterStateHydrated(function (Forms\Get $get, Forms\Set $set, $state) {
                                        // Si le slug est vide, le générer à partir du nom en français
                                        if (blank($state) && filled($get('nom.fr'))) {
                                            $set('slug', \Illuminate\Support\Str::slug($get('nom.fr')));
                                        }
                                    }),

                                Forms\Components\Select::make('categorie_parent_id')
                                    ->label('Catégorie parente')
                                    ->relationship('categorieParent', 'nom')
                                    ->searchable()
                                    ->preload()
                                    ->reactive()
                                    ->afterStateUpdated(function (Forms\Set $set) {
                                        // Réinitialiser le niveau et le chemin de catégorie
                                        // Ils seront calculés automatiquement lors de la sauvegarde
                                        $set('niveau', null);
                                        $set('category_path', null);
                                    }),

                                Forms\Components\TextInput::make('niveau')
                                    ->label('Niveau')
                                    ->numeric()
                                    ->disabled()
                                    ->dehydrated()
                                    ->helperText('Calculé automatiquement lors de la sauvegarde'),

                                Forms\Components\TextInput::make('category_path')
                                    ->label('Chemin de catégorie')
                                    ->disabled()
                                    ->dehydrated()
                                    ->helperText('Calculé automatiquement lors de la sauvegarde'),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),

                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Image')
                            ->schema([
                                self::configureImageUpload(
                                    Forms\Components\FileUpload::make('image_url')
                                        ->label('Image')
                                        ->image()
                                        ->imageEditor(),
                                    'categories'
                                ),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('image_url')
                    ->label('Image')
                    ->disk('public_images')
                    ->circular(),

                Tables\Columns\TextColumn::make('nom')
                    ->label('Nom')
                    ->formatStateUsing(function ($state) {
                        if (is_array($state)) {
                            return $state['fr'] ?? '';
                        } elseif (is_string($state)) {
                            try {
                                $decoded = json_decode($state, true);
                                if (is_array($decoded)) {
                                    return $decoded['fr'] ?? '';
                                }
                            } catch (\Exception $_) {
                                // Si le décodage échoue, retourner l'état tel quel
                            }
                        }
                        return $state;
                    })
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('slug')
                    ->label('Slug')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('categorieParent.nom')
                    ->label('Catégorie parente')
                    ->searchable(),

                Tables\Columns\TextColumn::make('niveau')
                    ->label('Niveau')
                    ->sortable(),

                Tables\Columns\TextColumn::make('category_path')
                    ->label('Chemin')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('produits_count')
                    ->label('Produits')
                    ->counts('produits')
                    ->sortable(),

                Tables\Columns\TextColumn::make('sous_categories_count')
                    ->label('Sous-catégories')
                    ->counts('sousCategories')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('categorie_parent_id')
                    ->label('Catégorie parente')
                    ->relationship('categorieParent', 'nom'),

                Tables\Filters\Filter::make('root_categories')
                    ->label('Catégories racines')
                    ->query(fn (Builder $query): Builder => $query->whereNull('categorie_parent_id')),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCategories::route('/'),
            'create' => Pages\CreateCategorie::route('/create'),
            'edit' => Pages\EditCategorie::route('/{record}/edit'),
        ];
    }
}
