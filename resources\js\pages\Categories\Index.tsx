import { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import { Category } from '@/models/Category';
import { CategoryService } from '@/services/CategoryService';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import { Grid2X2 } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from '@/components/ui/breadcrumb';

interface CategoriesProps {
  initialCategories?: Category[];
}

export default function Categories({ initialCategories = [] }: CategoriesProps) {
  const [categories, setCategories] = useState<Category[]>(initialCategories);
  const [isLoading, setIsLoading] = useState(initialCategories.length === 0);
  const categoryService = new CategoryService();

  // Récupérer les catégories si elles ne sont pas fournies
  useEffect(() => {
    if (initialCategories.length === 0) {
      const fetchCategories = async () => {
        setIsLoading(true);
        try {
          const mainCategories = await categoryService.getMainCategories();
          setCategories(mainCategories);
        } catch (error) {
          console.error('Erreur lors de la récupération des catégories:', error);
        } finally {
          setIsLoading(false);
        }
      };

      fetchCategories();
    }
  }, [initialCategories]);

  return (
    <EcommerceLayout>
      <Head title="Toutes les catégories" />

      <div className="container mx-auto px-4 py-8">
        {/* Fil d'Ariane */}
        <Breadcrumb className="mb-6">
          <BreadcrumbItem>
            <BreadcrumbLink href={route('home')}>Accueil</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbItem isCurrentPage>
            <BreadcrumbLink>Toutes les catégories</BreadcrumbLink>
          </BreadcrumbItem>
        </Breadcrumb>

        <div className="mb-6 flex items-center gap-3">
          <Grid2X2 className="h-6 w-6" />
          <h1 className="text-2xl font-bold">Toutes les catégories</h1>
        </div>

        <Separator className="mb-8" />

        {isLoading ? (
          <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
            {Array.from({ length: 10 }).map((_, index) => (
              <div key={index} className="flex flex-col items-center">
                <div className="mb-3 h-32 w-32 animate-pulse rounded-lg bg-muted"></div>
                <div className="h-5 w-24 animate-pulse rounded bg-muted"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
            {categories.map((category) => (
              <a
                key={category.id}
                href={route('category', { categorySlug: category.slug })}
                className="group flex flex-col items-center text-center"
              >
                <div className="mb-3 overflow-hidden rounded-lg border bg-card p-2 transition-all group-hover:border-primary group-hover:shadow-md">
                  {category.imageUrl ? (
                    <img
                      src={category.imageUrl}
                      alt={category.getTranslatedName()}
                      className="h-32 w-32 object-contain transition-transform group-hover:scale-105"
                      loading="lazy"
                    />
                  ) : (
                    <div className="flex h-32 w-32 items-center justify-center bg-muted">
                      <span className="text-sm text-muted-foreground">Pas d'image</span>
                    </div>
                  )}
                </div>
                <span className="text-base font-medium group-hover:text-primary">{category.getTranslatedName()}</span>
              </a>
            ))}
          </div>
        )}
      </div>
    </EcommerceLayout>
  );
}
