<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminDomainMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $currentDomain = $request->getHost();
        $currentPort = $request->getPort();
        
        // Ajouter le port si ce n'est pas 80 ou 443
        if (!in_array($currentPort, [80, 443])) {
            $currentDomain .= ':' . $currentPort;
        }

        // Obtenir le domaine admin configuré
        if (app()->environment('local')) {
            $adminDomain = config('domains.development.admin');
        } else {
            $adminDomain = config('domains.admin.domain');
        }

        // Vérifier si on est sur le domaine admin
        if ($currentDomain === $adminDomain) {
            // On est sur le domaine admin, vérifier les permissions
            if (!auth()->check()) {
                return redirect()->route('login');
            }

            if (auth()->user()->role !== 'Admin') {
                abort(403, 'Accès non autorisé à l\'administration.');
            }
        } else {
            // On n'est pas sur le domaine admin, bloquer l'accès aux routes admin
            if ($request->is('admin/*') || $request->is('admin')) {
                // Rediriger vers le domaine admin
                $url = $request->secure() ? 'https://' : 'http://';
                $url .= $adminDomain . '/' . ltrim($request->getRequestUri(), '/');
                
                return redirect($url, 301);
            }
        }

        return $next($request);
    }
}
