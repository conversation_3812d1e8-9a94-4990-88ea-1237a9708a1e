<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('banners', function (Blueprint $table) {
            // Rendre l'image optionnelle
            $table->string('image_url', 255)->nullable()->change();
            
            // Ajouter les nouveaux champs
            $table->string('title', 255)->nullable()->after('position');
            $table->text('description')->nullable()->after('title');
            $table->string('button_text', 100)->nullable()->after('description');
            $table->string('type', 50)->nullable()->after('button_text');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('banners', function (Blueprint $table) {
            // Supprimer les nouveaux champs
            $table->dropColumn(['title', 'description', 'button_text', 'type']);
            
            // Remettre l'image comme obligatoire
            $table->string('image_url', 255)->nullable(false)->change();
        });
    }
};
