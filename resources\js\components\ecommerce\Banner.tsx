import React, { useState, useEffect } from 'react';
import { Link } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Banner as BannerModel } from '@/models/Banner';

interface BannerProps {
  banner: BannerModel;
  className?: string;
}

/**
 * Composant pour afficher une bannière
 *
 * Ce composant s'adapte au type de bannière et affiche le contenu en conséquence
 */
export default function Banner({ banner, className = '' }: BannerProps) {
  const [isImageLoading, setIsImageLoading] = useState<boolean>(true);

  // Précharger l'image si elle existe
  useEffect(() => {
    if (banner.fullImageUrl) {
      const img = new Image();
      img.src = banner.fullImageUrl;
      img.onload = () => setIsImageLoading(false);
      img.onerror = () => setIsImageLoading(false);
    } else {
      setIsImageLoading(false);
    }
  }, [banner.fullImageUrl]);

  // Définir les styles en fonction du type de bannière
  const getBannerStyle = () => {
    const baseClasses = 'relative overflow-hidden rounded-xl';
    const gradientClasses = {
      // Types principaux
      'primary': 'bg-gradient-to-r from-blue-600 to-indigo-600',
      'secondary': 'bg-gradient-to-r from-purple-600 to-indigo-600',
      'promotional': 'bg-gradient-to-r from-amber-500 to-pink-500',

      // Types spécifiques pour le carousel
      'carousel-blue': 'bg-gradient-to-r from-blue-600 to-indigo-600',
      'carousel-amber': 'bg-gradient-to-r from-amber-500 to-orange-600',
      'carousel-emerald': 'bg-gradient-to-r from-emerald-600 to-teal-600',

      // Types spécifiques pour les bannières promotionnelles
      'promo-purple': 'bg-gradient-to-r from-purple-600 to-indigo-600',
      'promo-amber': 'bg-gradient-to-r from-amber-500 to-pink-500',
    };

    // Type par défaut si non spécifié
    const type = banner.type || 'primary';

    // Si le type n'existe pas dans notre mapping, utiliser le type primary par défaut
    const gradientClass = gradientClasses[type as keyof typeof gradientClasses] || gradientClasses['primary'];

    // Ajouter l'animation pulse pendant le chargement
    const loadingClass = isImageLoading ? 'animate-pulse' : '';

    return `${baseClasses} ${gradientClass} ${loadingClass} ${className}`;
  };

  // Définir les styles du bouton en fonction du type de bannière
  const getButtonStyle = () => {
    const buttonStyles = {
      // Types principaux
      'primary': 'bg-white text-blue-600 hover:bg-white/90',
      'secondary': 'border-white bg-black text-white hover:bg-white hover:text-purple-600',
      'promotional': 'border-white bg-black text-white hover:bg-white hover:text-pink-500',

      // Types spécifiques pour le carousel
      'carousel-blue': 'bg-white text-blue-600 hover:bg-white/90',
      'carousel-amber': 'bg-white text-orange-600 hover:bg-white/90',
      'carousel-emerald': 'bg-white text-emerald-600 hover:bg-white/90',

      // Types spécifiques pour les bannières promotionnelles
      'promo-purple': 'border-white bg-black text-white hover:bg-white hover:text-purple-600',
      'promo-amber': 'border-white bg-black text-white hover:bg-white hover:text-pink-500',
    };

    // Type par défaut si non spécifié
    const type = banner.type || 'primary';

    // Si le type n'existe pas dans notre mapping, utiliser le type primary par défaut
    return buttonStyles[type as keyof typeof buttonStyles] || buttonStyles['primary'];
  };

  return (
    <>
      {/* Si la bannière a une image, afficher uniquement l'image en plein écran */}
      {banner.fullImageUrl ? (
        <Link href={banner.targetUrl || '#'} className={`block relative aspect-[21/9] w-full rounded-xl overflow-hidden ${className} ${isImageLoading ? 'animate-pulse' : ''}`}>
          <div
            className={`absolute inset-0 bg-cover bg-center w-full h-full transition-opacity duration-300 ${isImageLoading ? 'opacity-70' : 'opacity-100'}`}
            style={{ backgroundImage: `url('${banner.fullImageUrl}')` }}
            aria-label={banner.title || 'Banner image'}
          />
        </Link>
      ) : (
        /* Si la bannière n'a pas d'image, afficher le contenu texte avec le gradient */
        <div className={getBannerStyle() + ' relative aspect-[21/9] w-full '}>
          <div className="absolute inset-0 flex flex-col items-center justify-center p-6 text-center text-white md:items-start md:text-left lg:p-12">
            {banner.title && <h3 className="mb-2 text-2xl font-bold md:text-3xl lg:text-4xl">{banner.title}</h3>}
            {banner.description && <p className="mb-4 max-w-md text-sm md:text-base lg:text-lg">{banner.description}</p>}
            {banner.buttonText && banner.targetUrl && (
              <Button
                asChild
                variant={banner.type?.startsWith('carousel') ? 'default' : 'outline'}
                className={getButtonStyle()}
              >
                <Link href={banner.targetUrl}>{banner.buttonText}</Link>
              </Button>
            )}
          </div>

          {/* Éléments décoratifs pour les bannières secondaires et promotionnelles */}
          {(banner.type === 'secondary' ||
            banner.type === 'promotional' ||
            banner.type?.startsWith('promo-')) && (
            <>
              <div className="absolute -bottom-4 -right-4 h-32 w-32 rounded-full bg-white/20"></div>
              <div className="absolute -top-4 right-12 h-16 w-16 rounded-full bg-white/20"></div>
            </>
          )}
        </div>
      )}
    </>
  );
}
