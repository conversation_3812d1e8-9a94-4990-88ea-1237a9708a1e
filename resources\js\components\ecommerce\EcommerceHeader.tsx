import { useState, useEffect } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { ShoppingCart, User, Menu, LogIn, Heart, ChevronRight, ArrowLeft, Grid2X2, ChevronDown } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import AppearanceToggleDropdown from '@/components/appearance-dropdown';
import BarreDeRecherche from './BarreDeRecherche';
import { useCart } from '@/contexts/CartContext';
import { useWishlist } from '@/contexts/WishlistContext';
import { CategoryService } from '@/services/CategoryService';
import { Category } from '@/models/Category';
import { type SharedData } from '@/types';
import UserMenuEcommerce from './UserMenuEcommerce';
import UserMenuGuest from './UserMenuGuest';
import MegaMenu from './MegaMenu';
import AddressDropdown from './AddressDropdown';
import LanguageSwitcher from './LanguageSwitcher';
import CurrencySwitcher from './CurrencySwitcher';
import { useTranslation } from '@/hooks/use-translation';
import CartSidebar from './CartSidebar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';

/**
 * Props pour le composant EcommerceHeader
 */
interface EcommerceHeaderProps {
  className?: string;
}

/**
 * Composant d'en-tête pour l'application e-commerce
 *
 * @param className - Classes CSS additionnelles
 */
/**
 * Type pour représenter un niveau de navigation dans le menu mobile
 */
interface NavigationLevel {
  categoryId: string | null;
  title: string;
  parentId: string | null;
}

export default function EcommerceHeader({ className = '' }: EcommerceHeaderProps) {
  const { itemCount, setCartOpen } = useCart();
  const { itemCount: wishlistCount } = useWishlist();
  const [categories, setCategories] = useState<Category[]>([]);
  // Nous n'avons plus besoin de ces états, mais nous gardons setIsScrolled pour la compatibilité avec le code existant
  const [, setIsScrolled] = useState(false);
  const { t, translate } = useTranslation();
  const { auth, addresses = [] } = usePage<SharedData>().props;
  const isAuthenticated = !!auth.user;
  const categoryService = new CategoryService();

  // États pour la navigation hiérarchique du menu mobile
  const [activeCategory, setActiveCategory] = useState<Category | null>(null);
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [isLoadingSubcategories, setIsLoadingSubcategories] = useState(false);
  const [navigationStack, setNavigationStack] = useState<NavigationLevel[]>([
    { categoryId: null, title: translate('header.categories'), parentId: null }
  ]);
  const [hasSubcategories, setHasSubcategories] = useState<Set<string>>(new Set());

  // Fonction pour ouvrir le modal du panier
  const openCartModal = () => {
    setCartOpen(true);
  };

  // Fonction pour naviguer vers une catégorie
  const navigateToCategory = async (category: Category) => {
    setIsLoadingSubcategories(true);
    setActiveCategory(category);

    try {
      const subs = await categoryService.getSubcategories(category.id);
      setSubcategories(subs);

      // Ajouter un niveau à la pile de navigation
      setNavigationStack([
        ...navigationStack,
        {
          categoryId: category.id,
          title: category.name,
          parentId: category.parentId
        }
      ]);
    } catch (error) {
      console.error(`Erreur lors de la récupération des sous-catégories pour ${category.getTranslatedName()}:`, error);
      setSubcategories([]);
    } finally {
      setIsLoadingSubcategories(false);
    }
  };

  // Fonction pour revenir au niveau précédent
  const navigateBack = async () => {
    if (navigationStack.length <= 1) return;

    // Retirer le dernier niveau
    const newStack = [...navigationStack];
    newStack.pop();
    setNavigationStack(newStack);

    // Récupérer le niveau précédent
    const previousLevel = newStack[newStack.length - 1];
    setIsLoadingSubcategories(true);

    try {
      if (previousLevel.categoryId === null) {
        // Si on revient au niveau racine, charger les catégories principales
        setActiveCategory(null);
        setSubcategories([]);
      } else {
        // Sinon, charger les sous-catégories du niveau précédent
        const category = await categoryService.getCategoryById(previousLevel.categoryId);
        setActiveCategory(category);

        if (category) {
          const subs = await categoryService.getSubcategories(category.id);
          setSubcategories(subs);
        }
      }
    } catch (error) {
      console.error('Erreur lors de la navigation arrière:', error);
    } finally {
      setIsLoadingSubcategories(false);
    }
  };

  // Vérifier quelles catégories ont des sous-catégories
  const checkForSubcategories = async (cats: Category[]) => {
    const withSubsSet = new Set<string>();

    for (const category of cats) {
      try {
        const subs = await categoryService.getSubcategories(category.id);
        if (subs.length > 0) {
          withSubsSet.add(category.id);
        }
      } catch (error) {
        console.error(`Erreur lors de la vérification des sous-catégories pour ${category.getTranslatedName()}:`, error);
      }
    }

    setHasSubcategories(withSubsSet);
  };

  // Effet pour détecter le défilement de la page
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      // Toujours considérer le header comme "scrolled" pour appliquer le style de fond et l'ombre
      setIsScrolled(true);
    };

    // Ajouter l'écouteur d'événement
    window.addEventListener('scroll', handleScroll);

    // Déclencher l'événement au chargement pour appliquer le style immédiatement
    handleScroll();

    // Nettoyer l'écouteur d'événement
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Récupération des catégories principales et vérification des sous-catégories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const mainCategories = await categoryService.getMainCategories();
        setCategories(mainCategories);

        // Vérifier quelles catégories ont des sous-catégories
        await checkForSubcategories(mainCategories);
      } catch (error) {
        console.error('Erreur lors de la récupération des catégories:', error);
      }
    };

    fetchCategories();
  }, []);

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 border-b bg-background shadow-md backdrop-blur-sm bg-background/90 ${className}`}>
      {/* Barre supérieure */}
      <div className={`container mx-auto flex items-center justify-between py-3 px-4`}>
        {/* Logo et menu mobile */}
        <div className="flex items-center gap-4">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">{t('header.menu')}</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="flex w-full flex-col p-0 sm:max-w-md">
              <SheetHeader className="border-b px-4 py-3">
                <SheetTitle className="flex items-center">
                  <Link href="/" className="text-xl font-bold">
                    Lorrelei
                  </Link>
                </SheetTitle>
              </SheetHeader>

              <div className="flex flex-1 flex-col">
                {/* Barre de navigation horizontale pour les catégories principales */}
                <div className="border-b">
                  <div className="overflow-x-auto scrollbar-styled">
                    <div className="flex items-center gap-2 p-3 whitespace-nowrap">
                      <Link
                        href={route('categories')}
                        className="flex shrink-0 items-center gap-1 rounded-md px-3 py-2 text-sm font-medium hover:bg-muted"
                      >
                        <Grid2X2 className="h-4 w-4" />
                        <span>{t('breadcrumb.categories')}</span>
                      </Link>

                      {categories.map((category) => (
                        <button
                          key={category.id}
                          className={`flex items-center shrink-0 rounded-md px-3 py-2 text-sm font-medium hover:bg-muted ${
                            navigationStack.length > 1 && navigationStack[1]?.categoryId === category.id
                              ? 'bg-primary/10 text-primary'
                              : ''
                          }`}
                          onClick={() => {
                            if (hasSubcategories.has(category.id)) {
                              navigateToCategory(category);
                            } else {
                              // Si pas de sous-catégories, rediriger vers la page de catégorie
                              window.location.href = route('category', { categorySlug: category.slug });
                            }
                          }}
                        >
                          {category.name}
                          {hasSubcategories.has(category.id) && (
                            <ChevronDown className="ml-1 h-3 w-3" />
                          )}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Contenu principal avec navigation hiérarchique */}
                <div className="flex-1 overflow-hidden">
                  <div className="relative h-full">
                    {/* En-tête du niveau actuel avec bouton retour si nécessaire */}
                    {navigationStack.length > 1 && (
                      <div className="flex items-center border-b p-3">
                        <button
                          onClick={navigateBack}
                          className="mr-2 flex items-center text-sm font-medium text-muted-foreground hover:text-foreground"
                        >
                          <ArrowLeft className="mr-1 h-4 w-4" />
                          {t('common.back')}
                        </button>
                        <span className="text-sm font-medium">
                          {navigationStack[navigationStack.length - 1].title}
                        </span>
                      </div>
                    )}

                    {/* Contenu du niveau actuel */}
                    <ScrollArea className="h-[calc(100vh-300px)]">
                      <div className="p-4">
                        {/* Affichage des sous-catégories */}
                        {isLoadingSubcategories ? (
                          <div className="grid grid-cols-2 gap-4">
                            {Array.from({ length: 4 }).map((_, index) => (
                              <div key={index} className="flex flex-col items-center">
                                <div className="mb-2 h-16 w-16 animate-pulse rounded-full bg-muted"></div>
                                <div className="h-4 w-20 animate-pulse rounded bg-muted"></div>
                              </div>
                            ))}
                          </div>
                        ) : navigationStack.length > 1 ? (
                          subcategories.length > 0 ? (
                            <div className="grid grid-cols-2 gap-4">
                              {subcategories.map((subcategory) => (
                                <div key={subcategory.id} className="flex flex-col">
                                  <button
                                    className="group flex flex-col items-center text-center"
                                    onClick={() => {
                                      if (hasSubcategories.has(subcategory.id)) {
                                        navigateToCategory(subcategory);
                                      } else {
                                        // Si pas de sous-catégories, rediriger vers la page de catégorie
                                        window.location.href = route('category', { categorySlug: subcategory.slug });
                                      }
                                    }}
                                  >
                                    <div className="mb-2 overflow-hidden rounded-full border p-1 transition-all group-hover:border-primary group-hover:shadow-md">
                                      {subcategory.imageUrl ? (
                                        <img
                                          src={subcategory.imageUrl}
                                          alt={subcategory.name}
                                          className="h-16 w-16 rounded-full object-cover transition-transform group-hover:scale-105"
                                          loading="lazy"
                                        />
                                      ) : (
                                        <div className="flex h-16 w-16 items-center justify-center rounded-full bg-muted">
                                          <span className="text-xs font-semibold uppercase text-muted-foreground">
                                            {subcategory.name.charAt(0)}
                                          </span>
                                        </div>
                                      )}
                                    </div>
                                    <div className="flex items-center justify-center">
                                      <span className="text-sm font-medium group-hover:text-primary">
                                        {subcategory.name}
                                      </span>
                                      {hasSubcategories.has(subcategory.id) && (
                                        <ChevronRight className="ml-1 h-3 w-3" />
                                      )}
                                    </div>
                                  </button>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <div className="flex h-32 items-center justify-center">
                              <p className="text-sm text-muted-foreground">
                                {t('common.no_subcategories')}
                              </p>
                            </div>
                          )
                        ) : (
                          <div className="space-y-1">
                            {categories.map((category) => (
                              <div key={category.id} className="rounded-md hover:bg-muted">
                                <button
                                  className="flex w-full items-center justify-between px-3 py-2 text-sm"
                                  onClick={() => {
                                    if (hasSubcategories.has(category.id)) {
                                      navigateToCategory(category);
                                    } else {
                                      // Si pas de sous-catégories, rediriger vers la page de catégorie
                                      window.location.href = route('category', { categorySlug: category.slug });
                                    }
                                  }}
                                >
                                  <span>{category.name}</span>
                                  {hasSubcategories.has(category.id) && (
                                    <ChevronRight className="h-4 w-4" />
                                  )}
                                </button>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </ScrollArea>
                  </div>
                </div>

                {/* Pied de page avec liens utiles */}
                <div className="border-t p-4">
                  <div className="space-y-2">
                    <button
                      onClick={() => {
                        openCartModal();
                      }}
                      className="flex w-full items-center gap-2 rounded-md px-3 py-2 text-sm text-left hover:bg-muted"
                    >
                      <ShoppingCart className="h-4 w-4" />
                      {t('header.cart')}
                      {itemCount > 0 && (
                        <Badge variant="secondary" className="ml-auto">
                          {itemCount > 99 ? '99+' : itemCount}
                        </Badge>
                      )}
                    </button>

                    {isAuthenticated ? (
                      <Link
                        href={route('my-account')}
                        className="flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-muted"
                      >
                        <User className="h-4 w-4" />
                        {t('header.account')}
                      </Link>
                    ) : (
                      <>
                        <Link
                          href={route('login')}
                          className="flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-muted"
                        >
                          <LogIn className="h-4 w-4" />
                          {t('header.login')}
                        </Link>
                        <Link
                          href={route('register')}
                          className="flex items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-muted"
                        >
                          <User className="h-4 w-4" />
                          {t('header.register')}
                        </Link>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </SheetContent>
          </Sheet>

          <Link href={route('home')} className="text-xl font-bold">
            Lorrelei
          </Link>
        </div>

        {/* Barre de recherche */}
        <div className="hidden flex-1 px-4 md:block md:max-w-xl">
          <BarreDeRecherche />
        </div>

        {/* Actions utilisateur */}
        <div className="flex items-center gap-2 ">
          {/* Masquer les boutons de langue, devise et thème sur mobile */}
          <div className="hidden md:flex md:items-center md:gap-2">
            <LanguageSwitcher />
            <CurrencySwitcher />
            <AppearanceToggleDropdown />
          </div>

          {isAuthenticated && (
            <AddressDropdown addresses={addresses} />
          )}

          <Link href={route('wishlist')}>
            <Button variant="ghost" size="icon" className="relative cursor-pointer">
              <Heart className="h-5 w-5" />
              {wishlistCount > 0 && (
                <Badge
                  variant="secondary"
                  className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full p-0 text-xs"
                >
                  {wishlistCount > 99 ? '99+' : wishlistCount}
                </Badge>
              )}
              <span className="sr-only">{t('header.wishlist')}</span>
            </Button>
          </Link>

          <Button
            variant="ghost"
            size="icon"
            className="relative cursor-pointer"
            onClick={openCartModal}
          >
            <ShoppingCart className="h-5 w-5" />
            {itemCount > 0 && (
              <Badge
                variant="secondary"
                className="absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full p-0 text-xs"
              >
                {itemCount > 99 ? '99+' : itemCount}
              </Badge>
            )}
            <span className="sr-only">{t('header.cart')}</span>
          </Button>

          {/* Menu utilisateur - différent sur mobile et desktop */}
          {isAuthenticated ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <User className="h-5 w-5" />
                  <span className="sr-only">{t('header.account')}</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56">
                <UserMenuEcommerce user={auth.user} />
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <>
              {/* Sur desktop, afficher les boutons de connexion et d'inscription */}
              <div className="hidden md:block">
                <Link href={route('login')}>
                  <Button variant="ghost" size="sm" className='cursor-pointer'>
                    <LogIn className="mr-2 h-4 w-4" />
                    <span>{t('header.login')}</span>
                  </Button>
                </Link>
                <Link href={route('register')}>
                  <Button variant="outline" size="sm" className='cursor-pointer'>
                    <span>{t('header.register')}</span>
                  </Button>
                </Link>
              </div>

              {/* Sur mobile, afficher le menu déroulant */}
              <div className="md:hidden">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <User className="h-5 w-5" />
                      <span className="sr-only">{t('header.account')}</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <UserMenuGuest />
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Navigation des catégories (desktop) */}
      <nav className="hidden border-t bg-muted/50 md:block">
        <div className="container mx-auto px-4">
          <MegaMenu categories={categories} />
        </div>
      </nav>

      {/* Barre de recherche mobile */}
      <div className="border-t p-2 md:hidden">
        <BarreDeRecherche />
      </div>

      {/* Modal du panier */}
      <CartSidebar />
    </header>
  );
}
