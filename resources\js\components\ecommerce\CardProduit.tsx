import { Product } from '@/models/Product';
import { useCart } from '@/contexts/CartContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Loader2, ShoppingCart, Star, MessageSquare } from 'lucide-react';
import { Link } from '@inertiajs/react';
import { useState, useRef, useEffect } from 'react';
import { toast } from '@/hooks/use-toast';
import WishlistButton from './WishlistButton';
import { useTranslation } from '@/hooks/use-translation';
import ProductCardModal from './ProductCardModal';
import { DeliveryInfo } from '@/models/CartItem';

/**
 * Props pour le composant CardProduit
 */
interface CardProduitProps {
  product: Product;
  className?: string;
}

/**
 * Composant affichant une carte de produit avec image, nom, prix et bouton d'ajout au panier
 *
 * @param product - Le produit à afficher
 * @param className - Classes CSS additionnelles
 */
export default function CardProduit({ product, className = '' }: CardProduitProps) {
  const { addItem } = useCart();
  const [isLoading, setIsLoading] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [imagesLoaded, setImagesLoaded] = useState<Record<number, boolean>>({});
  const [showModal, setShowModal] = useState(false);
  const { t } = useTranslation();

  // Vérifier si le produit a des variantes ou des zones de livraison
  const hasVariants = product.variants.length > 0 ||
                     product.attributes.some(attr =>
                       attr.type === 'couleur' || attr.type === 'taille' || attr.type === 'matiere'
                     );

  // Extraire les couleurs disponibles pour l'affichage
  const availableColors = product.variants
    .filter(variant => variant.attributes.some(attr => attr.type === 'couleur' && 'nom' in attr))
    .map(variant => {
      const colorAttr = variant.attributes.find(attr => attr.type === 'couleur' && 'nom' in attr);
      return colorAttr && 'nom' in colorAttr ? colorAttr.nom : null;
    })
    .filter((color): color is string => color !== null);

  // Supprimer les doublons
  const uniqueColors = [...new Set(availableColors)].slice(0, 3);

  // Précharger les images du produit
  useEffect(() => {
    const loadImage = (url: string, index: number) => {
      const img = new Image();
      img.src = url;
      img.onload = () => {
        setImagesLoaded(prev => ({
          ...prev,
          [index]: true
        }));
      };
      img.onerror = () => {
        setImagesLoaded(prev => ({
          ...prev,
          [index]: true // Considérer comme chargée même en cas d'erreur pour éviter l'animation infinie
        }));
      };
    };

    // Charger les images principales
    product.mainImageUrls.forEach((url, index) => {
      loadImage(url, index);
    });

    // Charger l'image par défaut si nécessaire
    if (product.mainImageUrls.length === 0 && product.imageUrl) {
      loadImage(product.imageUrl, 0);
    }
  }, [product.mainImageUrls, product.imageUrl]);

  // Vérifier si toutes les images sont chargées
  const areAllImagesLoaded = () => {
    if (product.mainImageUrls.length === 0) {
      return imagesLoaded[0] === true;
    }

    return product.mainImageUrls.every((_, index) => imagesLoaded[index] === true);
  };

  /**
   * Gère l'ajout du produit au panier
   *
   * @param e - L'événement de clic
   */
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Si le produit a des variantes ou des attributs, ouvrir le modal
    if (hasVariants) {
      setShowModal(true);
      return;
    }

    // Si le produit n'a pas de zones de livraison configurées
    if (product.availableZones === 0) {
      // Afficher un message "Fonctionnalité en développement"
      toast({
        title: t('common.feature_in_development'),
        description: t('common.contact_merchant_not_available'),
        variant: "default",
      });
      return;
    }

    // Pour les produits simples sans variantes, ajouter directement au panier
    setIsLoading(true);

    // Simuler un délai pour montrer le loader (peut être supprimé en production)
    setTimeout(() => {
      addItem(product);
      setIsLoading(false);

      // Afficher une notification toast
      toast({
        title: t('common.added_to_cart'),
        description: t('common.added_to_cart_description', {
          quantity: 1,
          product: product.name,
          plural: t('common.added_to_cart_plural_one')
        }),
        variant: "success",
      });
    }, 600);
  };

  /**
   * Gère l'ajout du produit au panier depuis le modal
   */
  const handleAddToCartFromModal = (productToAdd: Product, quantity: number, deliveryInfo?: DeliveryInfo) => {
    addItem(productToAdd, quantity, deliveryInfo);
  };
  return (
    <Card className={`overflow-hidden transition-all hover:shadow-md ${className}`}>
      <Link href={route('product', { productSlug: product.slug })} className="block">
        <div
          className={`relative aspect-square overflow-hidden ${!areAllImagesLoaded() ? 'animate-pulse bg-muted' : ''}`}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Image principale avec effet de transition */}
          {product.mainImageUrls.length > 0 && (
            <>
              <img
                src={product.mainImageUrls[0]}
                alt={product.name}
                className={`absolute inset-0 h-full w-full object-cover transition-opacity duration-300 ${
                  isHovered && product.mainImageUrls.length > 1 ? 'opacity-0' : 'opacity-100'
                } ${!imagesLoaded[0] ? 'opacity-0' : ''}`}
                onLoad={() => {
                  setImagesLoaded(prev => ({ ...prev, [0]: true }));
                }}
              />

              {/* Image secondaire pour l'effet de survol */}
              {product.mainImageUrls.length > 1 && (
                <img
                  src={product.mainImageUrls[1]}
                  alt={`${product.name} - Vue alternative`}
                  className={`absolute inset-0 h-full w-full object-cover transition-opacity duration-300 ${
                    isHovered ? 'opacity-100' : 'opacity-0'
                  } ${!imagesLoaded[1] ? 'opacity-0' : ''}`}
                  onLoad={() => {
                    setImagesLoaded(prev => ({ ...prev, [1]: true }));
                  }}
                />
              )}
            </>
          )}

          {/* Fallback si aucune image principale n'est disponible */}
          {product.mainImageUrls.length === 0 && (
            <img
              src={product.imageUrl}
              alt={product.name}
              className={`h-full w-full object-cover transition-transform duration-300 hover:scale-105 ${!imagesLoaded[0] ? 'opacity-0' : ''}`}
              onLoad={() => {
                setImagesLoaded(prev => ({ ...prev, [0]: true }));
              }}
            />
          )}

          {!product.inStock && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50">
              <span className="rounded-md bg-red-500 px-3 py-1 text-sm font-medium text-white">
                {t('common.out_of_stock')}
              </span>
            </div>
          )}

          {/* Bouton wishlist */}
          <div className="absolute right-2 top-2">
            <WishlistButton
              product={product}
              variant="outline"
              className="h-8 w-8 rounded-full bg-background/80 opacity-0 transition-opacity group-hover:opacity-100"
            />
          </div>

          {/* Affichage des variantes de couleur */}
          {uniqueColors.length > 0 && (
            <div className="absolute bottom-2 left-2 flex -space-x-1">
              {uniqueColors.map((color, index) => (
                <div
                  key={color}
                  className="w-5 h-5 rounded-full border border-white shadow-sm hover:scale-110 transition-transform"
                  style={{
                    backgroundColor: color.toLowerCase(),
                    zIndex: uniqueColors.length - index
                  }}
                  title={color}
                />
              ))}
              {availableColors.length > 3 && (
                <div className="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-xs border border-white shadow-sm">
                  +{availableColors.length - 3}
                </div>
              )}
            </div>
          )}
        </div>

        <CardContent className="p-4">
          {product.reviews > 0 && (
            <div className="mb-1 flex items-center">
              <div className="flex items-center text-amber-500">
                <Star className="mr-1 h-4 w-4 fill-current" />
                <span className="text-sm font-medium">{product.rating.toFixed(1)}</span>
              </div>
              <span className="ml-2 text-xs text-muted-foreground">
                ({product.reviews} {t('common.reviews')})
              </span>
            </div>
          )}

          <h3 className="mb-1 line-clamp-1 text-base font-medium">{product.name}</h3>

          <p className="mb-2 line-clamp-2 text-sm text-muted-foreground">
            {product.description}
          </p>

          <div className="flex items-center gap-2">
            <div className="text-lg font-semibold">{product.formattedPrice()}</div>

            {product.isOnSale() && (
              <>
                <div className="text-sm text-muted-foreground line-through">
                  {product.formattedOriginalPrice()}
                </div>
                <div className="rounded-md bg-red-100 px-1.5 py-0.5 text-xs font-medium text-red-800">
                  {t('common.discount_percentage', { percentage: product.getDiscountPercentage() || 0 })}
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Link>

      <CardFooter className="border-t p-4">
        {product.availableZones === 0 ? (
          <Button
            onClick={handleAddToCart}
            className="w-full cursor-pointer text-xs md:text-sm"
            variant="outline"
          >
            <MessageSquare className="md:mr-2 h-4 w-4" />
            {t('common.contact_merchant')}
          </Button>
        ) : (
          <Button
            onClick={handleAddToCart}
            className="w-full cursor-pointer text-xs md:text-sm"
            disabled={!product.inStock || isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="md:mr-2 h-4 w-4 animate-spin" />
                {t('common.adding_to_cart')}
              </>
            ) : (
              <>
                <ShoppingCart className="md:mr-2 h-4 w-4" />
                {t('common.add_to_cart')}
              </>
            )}
          </Button>
        )}
      </CardFooter>

      {/* Modal pour la sélection des variantes et des zones de livraison */}
      <ProductCardModal
        product={product}
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onAddToCart={handleAddToCartFromModal}
      />
    </Card>
  );
}
