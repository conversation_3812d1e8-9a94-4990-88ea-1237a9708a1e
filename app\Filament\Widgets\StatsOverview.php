<?php

namespace App\Filament\Widgets;

use App\Models\Commande;
use App\Models\Produit;
use App\Models\User;
use App\Models\Marchand;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class StatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        // Statistiques des utilisateurs
        $totalUsers = User::count();
        $totalClients = User::where('role', 'Client')->count();
        $totalMarchands = User::where('role', 'Marchand')->count();

        // Statistiques des commandes
        $totalCommandes = Commande::count();
        $commandesEnCours = Commande::whereIn('statut', ['EnAttente', 'EnCoursDeTraitement'])->count();
        $commandesLivrees = Commande::where('statut', 'Livré')->count();

        // Statistiques des ventes
        $ventesTotales = Commande::sum('montantTotal');
        $ventesAujourdhui = Commande::whereDate('creeLe', now()->toDateString())->sum('montantTotal');

        // Statistiques des produits
        $totalProduits = Produit::count();
        $produitsEnRupture = Produit::where('stock', 0)->count();

        return [
            Stat::make('Utilisateurs', $totalUsers)
                ->description($totalClients . ' clients, ' . $totalMarchands . ' marchands')
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),

            Stat::make('Commandes', $totalCommandes)
                ->description($commandesEnCours . ' en cours, ' . $commandesLivrees . ' livrées')
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('warning'),

            Stat::make('Ventes totales', number_format($ventesTotales, 2) . ' €')
                ->description('Aujourd\'hui: ' . number_format($ventesAujourdhui, 2) . ' €')
                ->descriptionIcon('heroicon-m-currency-euro')
                ->color('success'),

            Stat::make('Produits', $totalProduits)
                ->description($produitsEnRupture . ' en rupture de stock')
                ->descriptionIcon('heroicon-m-shopping-bag')
                ->color($produitsEnRupture > 0 ? 'danger' : 'success'),
        ];
    }
}
