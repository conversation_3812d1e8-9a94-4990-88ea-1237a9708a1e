<?php

namespace App\Console\Commands;

use App\Models\Produit;
use App\Models\Categorie;
use App\Models\Banner;
use App\Models\Review;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class MigrateImagesCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'images:migrate
                            {--debug : Afficher plus d\'informations de débogage}
                            {--dry-run : Vérifier les images sans les déplacer}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migre les images existantes vers le nouveau système de stockage basé sur l\'ID';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Script de migration des images vers le nouveau système de stockage');
        $this->info('===============================================================');

        // 1. Migration des images de produits
        $this->info("\nMigration des images de produits...");
        $produits = Produit::all();
        $totalProduits = $produits->count();
        $migratedProduits = 0;

        $progressBar = $this->output->createProgressBar($totalProduits);
        $progressBar->start();

        foreach ($produits as $produit) {
            $images = $produit->images;

            // Si images est une chaîne JSON, la décoder
            if (is_string($images)) {
                $images = json_decode($images, true) ?? [];
            }

            if (!empty($images)) {
                $newImages = [];
                $folderPrefix = $this->getFolderPrefix($produit->id);
                $newFolder = "images/products/{$folderPrefix}";
                $this->createFolderIfNotExists(public_path($newFolder));

                foreach ($images as $image) {
                    // Vérifier si l'image est déjà dans le nouveau format
                    if (strpos($image, "images/products/{$folderPrefix}/") !== false) {
                        $newImages[] = $image;
                        continue;
                    }

                    // Extraire le nom du fichier
                    $filename = basename($image);
                    $oldPath = $image;
                    $newPath = "{$newFolder}/{$filename}";

                    if ($this->moveImage($oldPath, $newPath, "produit")) {
                        $newImages[] = $newPath;
                    } else {
                        $newImages[] = $image; // Garder l'ancien chemin si l'image n'a pas pu être déplacée
                    }
                }

                // Mettre à jour l'enregistrement avec les nouveaux chemins d'images
                if (!$this->option('dry-run')) {
                    $produit->images = $newImages;
                    $produit->save();
                }
                $migratedProduits++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->info("\nMigration des produits terminée: $migratedProduits/$totalProduits produits migrés");

        // 2. Migration des images de catégories
        $this->info("\nMigration des images de catégories...");
        $categories = Categorie::all();
        $totalCategories = $categories->count();
        $migratedCategories = 0;

        $progressBar = $this->output->createProgressBar($totalCategories);
        $progressBar->start();

        foreach ($categories as $categorie) {
            if (!empty($categorie->image_url)) {
                $folderPrefix = $this->getFolderPrefix($categorie->id);
                $newFolder = "images/categories/{$folderPrefix}";
                $this->createFolderIfNotExists(public_path($newFolder));

                // Vérifier si l'image est déjà dans le nouveau format
                if (strpos($categorie->image_url, "images/categories/{$folderPrefix}/") !== false) {
                    $progressBar->advance();
                    continue;
                }

                // Extraire le nom du fichier
                $filename = basename($categorie->image_url);
                $oldPath = $categorie->image_url;
                $newPath = "{$newFolder}/{$filename}";

                if ($this->moveImage($oldPath, $newPath, "catégorie")) {
                    if (!$this->option('dry-run')) {
                        $categorie->image_url = $newPath;
                        $categorie->save();
                    }
                    $migratedCategories++;
                }
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->info("\nMigration des catégories terminée: $migratedCategories/$totalCategories catégories migrées");

        // 3. Migration des images de bannières
        $this->info("\nMigration des images de bannières...");
        $banners = Banner::all();
        $totalBanners = $banners->count();
        $migratedBanners = 0;

        $progressBar = $this->output->createProgressBar($totalBanners);
        $progressBar->start();

        foreach ($banners as $banner) {
            if (!empty($banner->image_url)) {
                // Pour les bannières avec UUID, utiliser les 2 premiers caractères
                $folderPrefix = substr($banner->id, 0, 2);
                $newFolder = "images/banners/{$folderPrefix}";
                $this->createFolderIfNotExists(public_path($newFolder));

                // Vérifier si l'image est déjà dans le nouveau format
                if (strpos($banner->image_url, "images/banners/{$folderPrefix}/") !== false) {
                    $progressBar->advance();
                    continue;
                }

                // Extraire le nom du fichier
                $filename = basename($banner->image_url);
                $oldPath = $banner->image_url;
                $newPath = "{$newFolder}/{$filename}";

                if ($this->moveImage($oldPath, $newPath, "bannière")) {
                    if (!$this->option('dry-run')) {
                        $banner->image_url = $newPath;
                        $banner->save();
                    }
                    $migratedBanners++;
                }
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->info("\nMigration des bannières terminée: $migratedBanners/$totalBanners bannières migrées");

        // 4. Migration des images d'avis
        $this->info("\nMigration des images d'avis...");
        $reviews = Review::all();
        $totalReviews = $reviews->count();
        $migratedReviews = 0;

        $progressBar = $this->output->createProgressBar($totalReviews);
        $progressBar->start();

        foreach ($reviews as $review) {
            $images = $review->images;

            if (!empty($images)) {
                $newImages = [];
                $folderPrefix = $this->getFolderPrefix($review->produit_id);
                $newFolder = "images/reviews/{$folderPrefix}";
                $this->createFolderIfNotExists(public_path($newFolder));

                foreach ($images as $image) {
                    // Si l'image est déjà au nouveau format (tableau avec name et folder)
                    if (is_array($image) && isset($image['name']) && isset($image['folder'])) {
                        $newImages[] = $image;
                        continue;
                    }

                    // Extraire le nom du fichier
                    $filename = basename(is_string($image) ? $image : '');
                    if (empty($filename)) continue;

                    $oldPath = is_string($image) ? $image : '';
                    $newPath = "{$newFolder}/{$filename}";

                    if ($this->moveImage($oldPath, $newPath, "avis")) {
                        $newImages[] = [
                            'name' => $filename,
                            'folder' => $folderPrefix
                        ];
                    } else {
                        // Garder l'ancien format si l'image n'a pas pu être déplacée
                        $newImages[] = $image;
                    }
                }

                // Mettre à jour l'enregistrement avec les nouveaux chemins d'images
                if (!$this->option('dry-run')) {
                    $review->images = $newImages;
                    $review->save();
                }
                $migratedReviews++;
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->info("\nMigration des avis terminée: $migratedReviews/$totalReviews avis migrés");

        $this->info("\nMigration des images terminée avec succès!");

        return 0;
    }

    /**
     * Obtient le préfixe de dossier basé sur l'ID
     *
     * @param mixed $id
     * @return string
     */
    private function getFolderPrefix($id)
    {
        $idInt = (int)$id;
        return $idInt < 1000 ? '0' : substr((string)$idInt, 0, -3);
    }

    /**
     * Crée un dossier s'il n'existe pas
     *
     * @param string $path
     * @return void
     */
    private function createFolderIfNotExists($path)
    {
        if (!File::exists($path)) {
            if (!$this->option('dry-run')) {
                File::makeDirectory($path, 0755, true);
                $this->line("Dossier créé: $path");
            } else {
                $this->line("[DRY-RUN] Dossier qui serait créé: $path");
            }
        }
    }

    /**
     * Déplace une image vers le nouveau système de stockage
     *
     * @param string $oldPath
     * @param string $newPath
     * @param string $type
     * @return bool
     */
    private function moveImage($oldPath, $newPath, $type)
    {
        // Afficher les informations de débogage
        if ($this->option('debug')) {
            $this->info("Recherche de l'image: $oldPath");
        }

        // Liste des emplacements possibles à vérifier
        $possiblePaths = [
            public_path($oldPath),                                  // Chemin tel quel
            base_path('public/' . $oldPath),                        // Chemin avec public/ préfixé
            public_path(str_replace('images/', '', $oldPath)),      // Chemin sans le préfixe 'images/'
            base_path('public/images/' . $oldPath),                 // Chemin avec 'images/' préfixé
            public_path('images/' . $oldPath),                      // Chemin avec 'images/' préfixé (autre variante)
            base_path($oldPath),                                    // Chemin direct depuis la racine
            base_path('storage/app/public/' . $oldPath),            // Chemin dans storage
            public_path(basename($oldPath)),                        // Juste le nom du fichier dans public
            base_path('public/' . basename($oldPath)),              // Juste le nom du fichier dans public (autre variante)
            base_path('storage/app/public/' . basename($oldPath)),  // Juste le nom du fichier dans storage
        ];

        // Vérifier si le chemin contient un préfixe de type 'products/0/'
        if (preg_match('/(products|categories|banners|reviews)\/\d+\//', $oldPath, $matches)) {
            $category = $matches[1]; // 'products', 'categories', etc.
            $filename = basename($oldPath);

            // Ajouter des chemins possibles sans le préfixe de dossier
            $possiblePaths[] = public_path($category . '/' . $filename);
            $possiblePaths[] = base_path('public/' . $category . '/' . $filename);
            $possiblePaths[] = public_path('images/' . $category . '/' . $filename);
            $possiblePaths[] = base_path('public/images/' . $category . '/' . $filename);
        }

        // Vérifier si le chemin contient un UUID (format typique des noms de fichiers générés)
        if (preg_match('/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/i', $oldPath)) {
            // Extraire l'UUID du chemin
            preg_match('/([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/i', $oldPath, $matches);
            if (!empty($matches[1])) {
                $uuid = $matches[1];
                // Ajouter des chemins possibles avec l'UUID
                $possiblePaths[] = public_path($uuid . '.' . pathinfo($oldPath, PATHINFO_EXTENSION));
                $possiblePaths[] = base_path('public/' . $uuid . '.' . pathinfo($oldPath, PATHINFO_EXTENSION));
                $possiblePaths[] = base_path('storage/app/public/' . $uuid . '.' . pathinfo($oldPath, PATHINFO_EXTENSION));
            }
        }

        // Vérifier tous les emplacements possibles
        foreach ($possiblePaths as $index => $path) {
            if ($this->option('debug')) {
                $this->line("  Vérification de l'emplacement #" . ($index + 1) . ": " . $path);
            }

            if (File::exists($path)) {
                $this->info("  Image trouvée à l'emplacement: " . $path);

                // Créer le dossier de destination s'il n'existe pas
                $newDir = dirname(public_path($newPath));
                $this->createFolderIfNotExists($newDir);

                // Déplacer l'image (sauf en mode dry-run)
                if (!$this->option('dry-run')) {
                    File::copy($path, public_path($newPath));
                    $this->line("  Image $type déplacée: $path -> " . public_path($newPath));
                } else {
                    $this->line("  [DRY-RUN] Image $type serait déplacée: $path -> " . public_path($newPath));
                }
                return true;
            }
        }

        // Recherche dans tous les dossiers d'images
        $imageDirectories = [
            'products', 'categories', 'banners', 'reviews'
        ];

        $filename = basename($oldPath);
        if ($this->option('debug')) {
            $this->line("  Recherche du fichier par nom: " . $filename);
        }

        foreach ($imageDirectories as $dir) {
            $searchPath = base_path('public/images/' . $dir);
            if (File::exists($searchPath)) {
                if ($this->option('debug')) {
                    $this->line("  Recherche dans le dossier: " . $searchPath);
                }

                // Recherche récursive dans le dossier
                $files = File::allFiles($searchPath);
                foreach ($files as $file) {
                    if ($file->getFilename() === $filename) {
                        $this->info("  Image trouvée par nom de fichier: " . $file->getPathname());

                        // Créer le dossier de destination s'il n'existe pas
                        $newDir = dirname(public_path($newPath));
                        $this->createFolderIfNotExists($newDir);

                        // Déplacer l'image (sauf en mode dry-run)
                        if (!$this->option('dry-run')) {
                            File::copy($file->getPathname(), public_path($newPath));
                            $this->line("  Image $type déplacée (trouvée par nom): " . $file->getPathname() . " -> " . public_path($newPath));
                        } else {
                            $this->line("  [DRY-RUN] Image $type serait déplacée (trouvée par nom): " . $file->getPathname() . " -> " . public_path($newPath));
                        }
                        return true;
                    }
                }
            }
        }

        $this->warn("Image $type introuvable: $oldPath (vérifiée à plusieurs emplacements)");
        return false;
    }
}
