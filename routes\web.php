<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Filament\Resources\ImportResource\Pages\ImportIndex as AdminImportIndex;
use App\Filament\Marchand\Resources\ImportResource\Pages\ImportIndex as MerchantImportIndex;
use App\Http\Controllers\CurrencyController;

/*
|--------------------------------------------------------------------------
| Web Routes - Domaine Principal
|--------------------------------------------------------------------------
|
| Routes pour le site principal (lorelei.com)
|
*/

// Domaine principal - Site e-commerce
Route::domain(config('domains.main.domain'))->group(function () {
Route::get('/welcome', function () {
    return Inertia::render('welcome');
})->name('welcome');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Redirection de /profile vers /settings/profile
    Route::redirect('profile', 'settings/profile');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

// Les routes e-commerce sont à la racine du site
require __DIR__.'/ecommerce.php';

// Routes d'administration
require __DIR__.'/admin.php';

// Routes pour les modèles d'importation
Route::get('/admin/importations/download-categories-template', [AdminImportIndex::class, 'downloadCategoriesTemplate'])
    ->name('filament.admin.resources.importations.download-categories-template')
    ->middleware(['web', 'auth']);

Route::get('/admin/importations/download-products-template', [AdminImportIndex::class, 'downloadProductsTemplate'])
    ->name('filament.admin.resources.importations.download-products-template')
    ->middleware(['web', 'auth']);

// Route fallback pour les pages 404
Route::fallback(function () {
    return Inertia::render('not-found');
});

Route::get('/merchant/importations/download-products-template', [MerchantImportIndex::class, 'downloadProductsTemplate'])
    ->name('filament.marchand.resources.importations.download-products-template')
    ->middleware(['web', 'auth']);

    // Routes pour la gestion des devises
    Route::post('/currency/change', [CurrencyController::class, 'change'])->name('currency.change');

    // Les routes e-commerce sont à la racine du site
    require __DIR__.'/ecommerce.php';

    // Routes d'authentification
    require __DIR__.'/auth.php';
    require __DIR__.'/settings.php';
});

/*
|--------------------------------------------------------------------------
| Web Routes - Domaine Admin
|--------------------------------------------------------------------------
|
| Routes pour l'administration (admin.lorelei.com)
|
*/

// Domaine admin - Interface d'administration
Route::domain(config('domains.admin.domain'))->middleware(['admin.domain'])->group(function () {
    // Routes d'administration
    require __DIR__.'/admin.php';

    // Routes pour les modèles d'importation admin
    Route::get('/importations/download-categories-template', [AdminImportIndex::class, 'downloadCategoriesTemplate'])
        ->name('filament.admin.resources.importations.download-categories-template')
        ->middleware(['web', 'auth']);

    Route::get('/importations/download-products-template', [AdminImportIndex::class, 'downloadProductsTemplate'])
        ->name('filament.admin.resources.importations.download-products-template')
        ->middleware(['web', 'auth']);
});

/*
|--------------------------------------------------------------------------
| Web Routes - Domaine Marchand
|--------------------------------------------------------------------------
|
| Routes pour les marchands (seller.lorelei.com)
|
*/

// Domaine marchand - Interface marchands
Route::domain(config('domains.seller.domain'))->middleware(['seller.domain'])->group(function () {
    // Routes pour les modèles d'importation marchand
    Route::get('/importations/download-products-template', [MerchantImportIndex::class, 'downloadProductsTemplate'])
        ->name('filament.marchand.resources.importations.download-products-template')
        ->middleware(['web', 'auth']);
});

// Route fallback pour les pages 404 (tous domaines)
Route::fallback(function () {
    return Inertia::render('not-found');
});
