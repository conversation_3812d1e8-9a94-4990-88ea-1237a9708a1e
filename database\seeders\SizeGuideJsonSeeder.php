<?php

namespace Database\Seeders;

use App\Models\Size;
use App\Models\SizeGuide;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;

class SizeGuideJsonSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Importer les tailles standard
        $this->importStandardSizes();

        // Importer les tailles numériques
        $this->importNumericSizes();

        // Importer les tailles de chaussures
        $this->importShoeSizes();

        // Importer les guides de tailles
        $this->importSizeGuides();
    }

    /**
     * Importer les tailles standard depuis le fichier JSON
     */
    private function importStandardSizes(): void
    {
        $jsonPath = resource_path('js/data/size-guides.json');

        if (File::exists($jsonPath)) {
            $data = json_decode(File::get($jsonPath), true);

            if (isset($data['standard_sizes']) && is_array($data['standard_sizes'])) {
                foreach ($data['standard_sizes'] as $sizeData) {
                    // Vérifier si la taille existe déjà avec le même code
                    $existingSize = Size::where('code', $sizeData['code'])->first();

                    if ($existingSize) {
                        // Mettre à jour uniquement le champ order
                        $existingSize->order = $sizeData['order'] ?? null;
                        $existingSize->save();
                    } else {
                        // Créer une nouvelle taille
                        Size::create([
                            'code' => $sizeData['code'],
                            'category' => 'clothing',
                            'name_fr' => $sizeData['full_name']['fr'],
                            'name_en' => $sizeData['full_name']['en'],
                            'is_active' => $sizeData['is_active'] ?? true,
                            'order' => $sizeData['order'] ?? null,
                        ]);
                    }
                }

                $this->command->info('Standard sizes imported successfully.');
            }
        }
    }

    /**
     * Importer les tailles numériques depuis le fichier JSON
     */
    private function importNumericSizes(): void
    {
        $jsonPath = resource_path('js/data/size-guides-additional.json');

        if (File::exists($jsonPath)) {
            $data = json_decode(File::get($jsonPath), true);

            if (isset($data['numeric_sizes']) && is_array($data['numeric_sizes'])) {
                foreach ($data['numeric_sizes'] as $sizeData) {
                    // Vérifier si la taille existe déjà avec le même code
                    $existingSize = Size::where('code', $sizeData['code'])->first();

                    if ($existingSize) {
                        // Mettre à jour uniquement le champ order
                        $existingSize->order = $sizeData['order'] ?? null;
                        $existingSize->save();
                    } else {
                        // Créer une nouvelle taille
                        Size::create([
                            'code' => $sizeData['code'],
                            'category' => $sizeData['category'] ?? 'clothing',
                            'name_fr' => $sizeData['full_name']['fr'],
                            'name_en' => $sizeData['full_name']['en'],
                            'is_active' => $sizeData['is_active'] ?? true,
                            'order' => $sizeData['order'] ?? null,
                        ]);
                    }
                }

                $this->command->info('Numeric sizes imported successfully.');
            }
        }
    }

    /**
     * Importer les tailles de chaussures depuis le fichier JSON
     */
    private function importShoeSizes(): void
    {
        $jsonPath = resource_path('js/data/size-guides-accessories.json');

        if (File::exists($jsonPath)) {
            $data = json_decode(File::get($jsonPath), true);

            if (isset($data['shoe_sizes']) && is_array($data['shoe_sizes'])) {
                foreach ($data['shoe_sizes'] as $sizeData) {
                    // Vérifier si la taille existe déjà avec le même code
                    $existingSize = Size::where('code', $sizeData['code'])->first();

                    if ($existingSize) {
                        // Mettre à jour uniquement les champs foot_length_cm et order
                        $existingSize->foot_length_cm = $sizeData['foot_length_cm'] ?? null;
                        $existingSize->order = $sizeData['order'] ?? null;
                        $existingSize->save();
                    } else {
                        // Créer une nouvelle taille
                        Size::create([
                            'code' => $sizeData['code'],
                            'category' => $sizeData['category'] ?? 'shoes',
                            'name_fr' => $sizeData['full_name']['fr'],
                            'name_en' => $sizeData['full_name']['en'],
                            'is_active' => $sizeData['is_active'] ?? true,
                            'order' => $sizeData['order'] ?? null,
                            'foot_length_cm' => $sizeData['foot_length_cm'] ?? null,
                        ]);
                    }
                }

                $this->command->info('Shoe sizes imported successfully.');
            }
        }
    }

    /**
     * Importer les guides de tailles depuis les fichiers JSON
     */
    private function importSizeGuides(): void
    {
        try {
            $jsonFiles = [
                resource_path('js/data/size-guides.json'),
                resource_path('js/data/size-guides-additional.json'),
                resource_path('js/data/size-guides-accessories.json'),
            ];

            foreach ($jsonFiles as $jsonPath) {
                if (File::exists($jsonPath)) {
                    $this->command->info("Processing file: " . $jsonPath);
                    $data = json_decode(File::get($jsonPath), true);

                    if (isset($data['size_guides']) && is_array($data['size_guides'])) {
                        foreach ($data['size_guides'] as $guideData) {
                            try {
                                // Convertir les types de mesures au format attendu par le modèle
                                $measurementTypes = [];
                                foreach ($guideData['measurement_types'] as $type) {
                                    $measurementTypes[] = [
                                        'name_fr' => $type['name']['fr'],
                                        'name_en' => $type['name']['en'],
                                        'code' => $type['id'],
                                        'unit' => $type['unit'] ?? 'cm',
                                        'description_fr' => $type['description']['fr'] ?? null,
                                        'description_en' => $type['description']['en'] ?? null,
                                    ];
                                }

                                // Convertir les systèmes de tailles au format attendu par le modèle
                                $sizeSystems = [];
                                foreach ($guideData['size_systems'] as $system) {
                                    $sizeSystems[] = [
                                        'name_fr' => $system['name']['fr'],
                                        'name_en' => $system['name']['en'],
                                        'code' => $system['id'],
                                    ];
                                }

                                // Vérifier si le guide existe déjà
                                $existingGuide = SizeGuide::where('name_fr', $guideData['name'])
                                    ->where('category', $guideData['category'])
                                    ->first();

                                if ($existingGuide) {
                                    // Mettre à jour le guide existant
                                    $existingGuide->measurement_types = $measurementTypes;
                                    $existingGuide->size_systems = $sizeSystems;
                                    $existingGuide->size_chart = $guideData['size_chart'];
                                    $existingGuide->instructions_fr = $guideData['instructions']['fr'] ?? null;
                                    $existingGuide->instructions_en = $guideData['instructions']['en'] ?? null;
                                    $existingGuide->image = $guideData['image'] ?? null;
                                    $existingGuide->save();

                                    $this->command->info("Updated size guide: " . $guideData['name']);
                                } else {
                                    // Créer un nouveau guide
                                    SizeGuide::create([
                                        'name_fr' => $guideData['name'],
                                        'name_en' => $guideData['name'],
                                        'category' => $guideData['category'],
                                        'measurement_types' => $measurementTypes,
                                        'size_systems' => $sizeSystems,
                                        'size_chart' => $guideData['size_chart'],
                                        'instructions_fr' => $guideData['instructions']['fr'] ?? null,
                                        'instructions_en' => $guideData['instructions']['en'] ?? null,
                                        'image' => $guideData['image'] ?? null,
                                        'is_active' => true,
                                    ]);

                                    $this->command->info("Created size guide: " . $guideData['name']);
                                }
                            } catch (\Exception $e) {
                                $this->command->error("Error processing guide: " . ($guideData['name'] ?? 'Unknown') . " - " . $e->getMessage());
                            }
                        }
                    }
                } else {
                    $this->command->warn("File not found: " . $jsonPath);
                }
            }

            // Importer les conseils d'ajustement
            $jsonPath = resource_path('js/data/measurement-instructions.json');
            if (File::exists($jsonPath)) {
                $data = json_decode(File::get($jsonPath), true);

                if (isset($data['fitting_tips']) && is_array($data['fitting_tips'])) {
                    // Mettre à jour tous les guides de tailles avec les conseils d'ajustement
                    $guides = SizeGuide::all();
                    foreach ($guides as $guide) {
                        $guide->fitting_tips = $data['fitting_tips'];
                        $guide->save();
                    }

                    $this->command->info('Fitting tips imported successfully.');
                }
            } else {
                $this->command->warn("File not found: " . $jsonPath);
            }

            $this->command->info('Size guides imported successfully.');
        } catch (\Exception $e) {
            $this->command->error("Error importing size guides: " . $e->getMessage());
        }
    }
}
