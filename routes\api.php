<?php

use App\Http\Controllers\Api\AddressController;
use App\Http\Controllers\Api\AddressUuidController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\BannerController;
use App\Http\Controllers\Api\CategorieController;
use App\Http\Controllers\Api\CurrencyController;
use App\Http\Controllers\Api\MarchandZoneLivraisonController;
use App\Http\Controllers\Api\PayementController;
use App\Http\Controllers\Api\ProduitController;
use App\Http\Controllers\Api\ProduitZoneLivraisonController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\SizeGuideController;
use App\Http\Controllers\Api\UserProfileController;
use App\Http\Controllers\Api\ZoneLivraisonController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Routes pour les catégories
Route::get('/categories', [CategorieController::class, 'index']);
Route::get('/categories/main', [CategorieController::class, 'getMainCategories']);
Route::get('/categories/slug/{slug}', [CategorieController::class, 'getBySlug']);
Route::get('/categories/{id}', [CategorieController::class, 'show']);
Route::get('/categories/{id}/subcategories', [CategorieController::class, 'getSubcategories']);
Route::get('/categories/{id}/parents', [CategorieController::class, 'getParents']);
Route::get('/categories/{id}/subcategories/featured', [CategorieController::class, 'getSubcategoriesWithFeaturedProducts']);

// Routes pour les bannières
Route::get('/banners', [BannerController::class, 'index']);
Route::get('/banners/position/{position}', [BannerController::class, 'getByPosition']);

// Routes pour les produits
Route::get('/produits', [ProduitController::class, 'index']);
Route::get('/produits/featured/{limit?}', [ProduitController::class, 'getFeatured']);
Route::get('/produits/search', [ProduitController::class, 'search']);
Route::get('/produits/slug/{slug}', [ProduitController::class, 'getBySlug']);
Route::get('/produits/categorie/{categorieId}', [ProduitController::class, 'getByCategorie']);
Route::get('/produits/discounted', [ProduitController::class, 'getDiscounted']);
// Routes pour vérifier la disponibilité de livraison (doivent être avant la route générique)
Route::get('/produits/delivery-check', [ProduitZoneLivraisonController::class, 'checkDeliveryAvailability']);
Route::get('/produits/delivery-check-with-children', [ProduitZoneLivraisonController::class, 'checkDeliveryAvailabilityWithChildren']);
Route::get('/produits/{id}', [ProduitController::class, 'show']);

// Routes pour les avis
Route::get('/produits/{productId}/reviews', [ReviewController::class, 'getProductReviews']);
Route::post('/produits/{productId}/reviews', [ReviewController::class, 'store']);
Route::post('/reviews/{reviewId}/vote', [ReviewController::class, 'vote']);

// Routes pour les devises
Route::get('/currencies', [CurrencyController::class, 'index']);
Route::get('/currencies/current', [CurrencyController::class, 'current']);
Route::post('/currencies/current', [CurrencyController::class, 'setCurrent']);
Route::get('/currencies/default', [CurrencyController::class, 'default']);
Route::post('/currencies/convert', [CurrencyController::class, 'convert']);

// Routes pour les guides de tailles
Route::get('/size-guides', [SizeGuideController::class, 'getAllGuides']);
Route::get('/size-guides/category/{categoryId}', [SizeGuideController::class, 'getForCategory']);
Route::get('/size-guides/product/{productId}', [SizeGuideController::class, 'getForProduct']);

// Routes pour les zones de livraison
Route::get('/zones-livraison', [ZoneLivraisonController::class, 'index']);
Route::get('/zones-livraison/tree', [ZoneLivraisonController::class, 'getTree']);
Route::get('/zones-livraison/{id}', [ZoneLivraisonController::class, 'show']);
Route::get('/zones-livraison/{id}/children', [ZoneLivraisonController::class, 'getChildren']);

// Routes pour les zones de livraison des produits et marchands
Route::get('/produits/{produitId}/zones-livraison', [ProduitZoneLivraisonController::class, 'getZonesByProduit']);
Route::get('/produits/{produitId}/available-zones', [ProduitZoneLivraisonController::class, 'getAvailableZonesForProduct']);
Route::get('/marchands/{marchandId}/zones-livraison', [MarchandZoneLivraisonController::class, 'getZonesByMarchand']);
Route::get('/zones-livraison/{zoneId}/marchands', [MarchandZoneLivraisonController::class, 'getMarchandsByZone']);

Route::middleware('auth:sanctum')->group(function () {
    // Routes pour les zones de livraison du marchand
    Route::apiResource('/marchand/zones-livraison', MarchandZoneLivraisonController::class);

    // Routes pour les produits-zones de livraison
    Route::apiResource('/produits-zones-livraison', ProduitZoneLivraisonController::class);

    // Autres routes protégées
});

Route::middleware(['auth'])->group(function () {
    // Route::post('/payment/create', [PayementController::class, 'createOrder'])->name('payment.create');
    // Route::post('/payment/capture/{orderId}', [PayementController::class, 'captureOrder'])->name('payment.capture');

    // routes/api.php
    Route::prefix('payment')->group(function () {
        Route::post('/create-order', [PayementController::class, 'createOrder']);
        Route::post('/execute', [PayementController::class, 'executePayment']);
        Route::get('/details/{paymentId}', [PayementController::class, 'getPaymentDetails']);
        Route::post('/refund', [PayementController::class, 'refundPayment']);
    });

    // Routes web pour les redirections
    Route::get('/payment/success', [PayementController::class, 'success'])->name('payment.success');
    Route::get('/payment/cancel', [PayementController::class, 'cancel'])->name('payment.cancel');
});

