import React, { useState } from 'react';
import { Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useWishlist } from '@/contexts/WishlistContext';
import { Product } from '@/models/Product';
import { toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { useTranslation } from '@/hooks/use-translation';

interface WishlistButtonProps {
  product: Product;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  showText?: boolean;
  disabled?: boolean;
}

/**
 * Bouton pour ajouter/supprimer un produit de la wishlist
 */
export default function WishlistButton({
  product,
  variant = 'outline',
  size = 'icon',
  className = '',
  showText = false,
  disabled = false
}: WishlistButtonProps) {
  const { isInWishlist, toggleWishlist } = useWishlist();
  const [isLoading, setIsLoading] = useState(false);
  const { t } = useTranslation();

  const isProductInWishlist = isInWishlist(product.id);

  /**
   * Gère le clic sur le bouton wishlist
   */
  const handleToggleWishlist = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsLoading(true);

    // Simuler un délai pour montrer le chargement (peut être supprimé en production)
    setTimeout(() => {
      const isAdded = toggleWishlist(product);

      // Afficher une notification toast
      toast({
        title: isAdded
          ? t('wishlist.added_to_wishlist')
          : t('wishlist.removed_from_wishlist'),
        description: isAdded
          ? t('wishlist.added_to_wishlist_description', { product: product.name })
          : t('wishlist.removed_from_wishlist_description', { product: product.name }),
        variant: isAdded ? "success" : "default",
      });

      setIsLoading(false);
    }, 300);
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={cn(
        isProductInWishlist && "text-red-500 hover:text-red-600",
        isLoading && "opacity-70 cursor-not-allowed",
        className
      )}
      onClick={handleToggleWishlist}
      disabled={isLoading || disabled}
      aria-label={isProductInWishlist ? t('wishlist.remove_from_wishlist') : t('wishlist.add_to_wishlist')}
    >
      <Heart
        className={cn(
          "h-4 w-4",
          isProductInWishlist && "fill-current"
        )}
      />
      {showText && (
        <span className="ml-2">
          {isProductInWishlist ? t('wishlist.remove_from_wishlist') : t('wishlist.add_to_wishlist')}
        </span>
      )}
    </Button>
  );
}
