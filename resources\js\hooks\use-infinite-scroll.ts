import { useState, useEffect, useRef, useCallback } from 'react';

interface UseInfiniteScrollOptions {
  threshold?: number;
  rootMargin?: string;
  initialPage?: number;
  enabled?: boolean;
  maxRetries?: number;
}

/**
 * Hook personnalisé pour gérer le chargement progressif des données lors du défilement
 *
 * @param callback - Fonction à appeler lorsque l'élément observé devient visible
 * @param options - Options de configuration
 * @returns Un objet contenant la référence à attacher à l'élément observé, la page actuelle, et des fonctions utilitaires
 */
export function useInfiniteScroll(
  callback: (page: number) => Promise<boolean>,
  {
    threshold = 0.1,
    rootMargin = '0px',
    initialPage = 1,
    enabled = true,
    maxRetries = 3
  }: UseInfiniteScrollOptions = {}
) {
  const [page, setPage] = useState<number>(initialPage);
  const [loading, setLoading] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const observer = useRef<IntersectionObserver | null>(null);
  const observerTarget = useRef<HTMLDivElement | null>(null);
  const retryCount = useRef<number>(0);
  const emptyPagesCount = useRef<number>(0);
  const previousProductsLength = useRef<number>(0);
  const isLoadingRef = useRef<boolean>(false); // Référence pour suivre l'état de chargement

  // Fonction pour réinitialiser l'état
  const reset = useCallback(() => {
    setPage(initialPage);
    setLoading(false);
    setHasMore(true);
    retryCount.current = 0;
    emptyPagesCount.current = 0;
    previousProductsLength.current = 0;
    isLoadingRef.current = false;
  }, [initialPage]);

  // Fonction pour charger plus de données
  const loadMore = useCallback(async () => {
    // Vérifier si déjà en cours de chargement, s'il n'y a plus de données, ou si désactivé
    if (isLoadingRef.current || !hasMore || !enabled) return;

    // Mettre à jour l'état de chargement
    isLoadingRef.current = true;
    setLoading(true);

    try {
      // Sauvegarder la longueur actuelle des données avant l'appel
      const currentLength = previousProductsLength.current;

      // Appeler la fonction de callback
      const hasMoreData = await callback(page);

      // Vérifier si de nouvelles données ont été ajoutées
      const newLength = previousProductsLength.current;

      if (currentLength === newLength && hasMoreData) {
        // Aucune nouvelle donnée n'a été ajoutée, mais hasMoreData est true
        // Incrémenter le compteur de pages vides
        emptyPagesCount.current += 1;
        console.warn(`Page ${page} n'a ajouté aucune nouvelle donnée. Compteur de pages vides: ${emptyPagesCount.current}`);

        if (emptyPagesCount.current >= 3) {
          // Si 3 pages consécutives n'ont pas ajouté de données, arrêter le chargement
          console.error(`Détection de boucle: ${emptyPagesCount.current} pages consécutives sans nouvelles données. Arrêt du chargement.`);
          setHasMore(false);
          return;
        }
      } else if (hasMoreData) {
        // Des données ont été ajoutées, réinitialiser le compteur de pages vides
        emptyPagesCount.current = 0;
        // Passer à la page suivante
        setPage(prevPage => prevPage + 1);
      }

      // Mettre à jour l'état hasMore
      setHasMore(hasMoreData);

      // Réinitialiser le compteur de tentatives en cas de succès
      retryCount.current = 0;
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      // Gérer les tentatives de nouvelle tentative
      retryCount.current += 1;
      if (retryCount.current >= maxRetries) {
        console.error(`Nombre maximum de tentatives atteint (${maxRetries}). Arrêt du chargement.`);
        setHasMore(false);
      }
    } finally {
      // Réinitialiser l'état de chargement
      isLoadingRef.current = false;
      setLoading(false);
    }
  }, [callback, page, hasMore, enabled, maxRetries]);

  // Configuration de l'observer
  useEffect(() => {
    if (!enabled) return;

    const options = {
      root: null, // viewport
      rootMargin,
      threshold,
    };

    observer.current = new IntersectionObserver(entries => {
      const [entry] = entries;
      // Seulement déclencher le chargement si l'élément est visible, s'il y a plus de données,
      // et si nous ne sommes pas déjà en train de charger
      if (entry.isIntersecting && hasMore && !isLoadingRef.current) {
        loadMore();
      }
    }, options);

    const currentTarget = observerTarget.current;
    if (currentTarget) {
      observer.current.observe(currentTarget);
    }

    return () => {
      if (currentTarget && observer.current) {
        observer.current.unobserve(currentTarget);
      }
    };
  }, [loadMore, hasMore, threshold, rootMargin, enabled]);

  // Référence à attacher à l'élément qui déclenchera le chargement
  const ref = useCallback((node: HTMLDivElement) => {
    observerTarget.current = node;
    if (observer.current && node) {
      observer.current.observe(node);
    }
  }, []);

  return {
    ref,
    page,
    loading,
    hasMore,
    loadMore,
    reset
  };
}

