<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Banner;

class BannerFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Banner::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'image_url' => fake()->regexify('[A-Za-z0-9]{255}'),
            'target_url' => fake()->regexify('[A-Za-z0-9]{255}'),
            'position' => fake()->regexify('[A-Za-z0-9]{100}'),
            'start_date' => fake()->dateTime(),
            'end_date' => fake()->dateTime(),
            'is_active' => fake()->boolean(),
            'priorite' => fake()->numberBetween(-10000, 10000),
        ];
    }
}
