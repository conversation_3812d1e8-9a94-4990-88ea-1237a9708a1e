<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('size_guides', function (Blueprint $table) {
            // Ajouter le champ fitting_tips pour les conseils d'ajustement
            $table->json('fitting_tips')->nullable()->after('size_chart')
                ->comment('Conseils d\'ajustement pour différents types de vêtements');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('size_guides', function (Blueprint $table) {
            $table->dropColumn('fitting_tips');
        });
    }
};
