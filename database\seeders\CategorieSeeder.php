<?php

namespace Database\Seeders;

use App\Models\Categorie;
use Illuminate\Database\Seeder;

class CategorieSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Catégories principales
        $categories = [
            [
                'nom' => 'Vêtements',
                'description' => 'Tous types de vêtements pour hommes, femmes et enfants',
                'image_url' => '/images/categories/clothing.png'
            ],
            [
                'nom' => 'Chaussures',
                'description' => 'Chaussures pour hommes, femmes et enfants',
                'image_url' => '/images/categories/shoes.png'
            ],
            [
                'nom' => 'Accessoires',
                'description' => 'Accessoires de mode',
                'image_url' => '/images/categories/accessories.png'
            ],
            [
                'nom' => 'Bijoux',
                'description' => 'Bijoux et montres',
                'image_url' => '/images/categories/jewelry.png'
            ],
            [
                'nom' => 'Électronique',
                'description' => 'Produits électroniques',
                'image_url' => '/images/categories/electronics.png'
            ],
        ];

        foreach ($categories as $category) {
            $mainCategory = Categorie::firstOrCreate(
                ['nom' => $category['nom']],
                [
                    'description' => $category['description'],
                    'image_url' => $category['image_url']
                ]
            );

            // Sous-catégories pour Vêtements
            if ($category['nom'] === 'Vêtements') {
                $subCategories = [
                    [
                        'nom' => 'Hommes',
                        'description' => 'Vêtements pour hommes',
                        'image_url' => '/images/categories/mens.png'
                    ],
                    [
                        'nom' => 'Femmes',
                        'description' => 'Vêtements pour femmes',
                        'image_url' => '/images/categories/womens.png'
                    ],
                    [
                        'nom' => 'Enfants',
                        'description' => 'Vêtements pour enfants',
                        'image_url' => '/images/categories/kids.png'
                    ],
                ];

                foreach ($subCategories as $subCategory) {
                    Categorie::firstOrCreate(
                        ['nom' => $subCategory['nom']],
                        [
                            'categorie_parent_id' => $mainCategory->id,
                            'description' => $subCategory['description'],
                            'image_url' => $subCategory['image_url']
                        ]
                    );
                }
            }

            // Sous-catégories pour Électronique
            if ($category['nom'] === 'Électronique') {
                $subCategories = [
                    [
                        'nom' => 'Smartphones',
                        'description' => 'Téléphones intelligents',
                        'image_url' => '/images/categories/smartphones.png'
                    ],
                    [
                        'nom' => 'Ordinateurs portables',
                        'description' => 'Ordinateurs portables et accessoires',
                        'image_url' => '/images/categories/laptops.png'
                    ],
                    [
                        'nom' => 'Tablettes',
                        'description' => 'Tablettes et liseuses',
                        'image_url' => '/images/categories/tablets.png'
                    ],
                ];

                foreach ($subCategories as $subCategory) {
                    Categorie::firstOrCreate(
                        ['nom' => $subCategory['nom']],
                        [
                            'categorie_parent_id' => $mainCategory->id,
                            'description' => $subCategory['description'],
                            'image_url' => $subCategory['image_url']
                        ]
                    );
                }
            }
        }
    }
}
