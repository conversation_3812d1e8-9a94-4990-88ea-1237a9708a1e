<?php

namespace App\Filament\Marchand\Pages;

use App\Filament\Marchand\Widgets\MarchandStatsOverview;
use App\Filament\Marchand\Widgets\LatestOrders;
use App\Filament\Marchand\Widgets\SalesChart;
use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected function getHeaderWidgets(): array
    {
        return [
            MarchandStatsOverview::class,
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            SalesChart::class,
            LatestOrders::class,
        ];
    }
}
