<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ImportResource\Pages;
use App\Models\Categorie;
use App\Models\Marchand;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ImportResource extends Resource
{
    protected static ?string $model = Categorie::class; // Utilisation fictive, juste pour la structure

    protected static ?string $navigationIcon = 'heroicon-o-arrow-up-tray';

    protected static ?string $navigationLabel = 'Importation';

    protected static ?string $pluralModelLabel = 'Importations';

    protected static ?string $modelLabel = 'Importation';

    protected static ?string $slug = 'importations';

    protected static ?string $navigationGroup = 'Outils';

    protected static ?int $navigationSort = 100;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Pas de formulaire nécessaire pour cette ressource
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Pas de tableau nécessaire pour cette ressource
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ImportIndex::route('/'),
        ];
    }
}
