<?php

namespace App\Filament\Resources\ImportResource\Pages;

use App\Filament\Resources\ImportResource;
use App\Imports\CategorieImport;
use App\Imports\ProduitImport;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Tabs\Tab;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Illuminate\Support\Facades\Storage;

class ImportIndex extends Page
{
    use InteractsWithForms;

    protected static string $resource = ImportResource::class;

    protected static string $view = 'filament.resources.import-resource.pages.import-index';

    protected static ?string $title = 'Importation de données';

    public ?array $data = [];

    // Propriétés publiques requises par Livewire
    public $categories_file = [];
    public $products_file = [];
    public $marchand_id = null;

    // Propriétés pour l'aperçu des catégories
    public $preview_categories = [];
    public $preview_total = 0;
    public $show_preview = false;

    // Propriétés pour le modal d'importation
    public $isImporting = false;
    public $importMessage = "Importation en cours...";

    public function mount(): void
    {
        $this->form->fill();
    }

    public function hydrate(): void
    {
        // S'assurer que les propriétés sont correctement initialisées
        if (!is_array($this->categories_file)) {
            $this->categories_file = [];
        }

        if (!is_array($this->products_file)) {
            $this->products_file = [];
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Import')
                    ->tabs([
                        Tab::make('Catégories')
                            ->schema([
                                Section::make('Importation de catégories')
                                    ->description('Importez des catégories à partir d\'un fichier CSV ou Excel')
                                    ->schema([
                                        FileUpload::make('categories_file')
                                            ->label('Fichier CSV/Excel')
                                            ->acceptedFileTypes(['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'])
                                            ->maxSize(10240) // 10MB
                                            ->directory('temp/imports')
                                            ->visibility('private')
                                            ->live(),
                                    ]),
                            ]),
                        Tab::make('Produits')
                            ->schema([
                                Section::make('Importation de produits')
                                    ->description('Importez des produits à partir d\'un fichier CSV ou Excel')
                                    ->schema([
                                        FileUpload::make('products_file')
                                            ->label('Fichier CSV/Excel')
                                            ->acceptedFileTypes(['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'])
                                            ->maxSize(10240) // 10MB
                                            ->directory('temp/imports')
                                            ->visibility('private')
                                            ->live(),
                                        Select::make('marchand_id')
                                            ->label('Marchand (optionnel)')
                                            ->options(function () {
                                                return \App\Models\Marchand::all()->pluck('nomEntreprise', 'id');
                                            })
                                            ->placeholder('Sélectionner un marchand')
                                            ->live()
                                            ->helperText('Si non sélectionné, le marchand connecté ou le premier marchand disponible sera utilisé'),
                                    ]),
                            ]),
                    ])
                    ->activeTab(1), // 0 pour le premier onglet (Catégories)
            ]);
    }

    public function importCategories()
    {
        // Récupérer les données du formulaire
        $data = $this->form->getState();
        if (empty($data['categories_file'])) {
            Notification::make()
            ->title('Erreur')
            ->body('Veuillez sélectionner un fichier à importer')
            ->danger()
            ->send();
            return;
        }

        // Activer le modal d'importation
        $this->isImporting = true;
        $this->importMessage = "Analyse du fichier de catégories en cours...";

        $filePath = $this->getFilePath($data['categories_file']);
        $import = new CategorieImport();

        try {
            // Analyser le fichier pour obtenir un aperçu des catégories
            $results = $import->import($filePath);

            // Stocker le chemin du fichier pour l'importation réelle
            session(['import_categories_file_path' => $filePath]);

            // Vérifier si les clés attendues existent
            if (!isset($results['categories']) || !isset($results['total'])) {
                // Désactiver le modal d'importation
                $this->isImporting = false;

                Notification::make()
                    ->title('Erreur')
                    ->body('Format de données incorrect lors de l\'analyse des catégories')
                    ->danger()
                    ->send();
                return;
            }

            // Stocker les données d'aperçu dans les propriétés publiques
            $this->preview_categories = $results['categories'];
            $this->preview_total = $results['total'];
            $this->show_preview = true;

            // Désactiver le modal d'importation
            $this->isImporting = false;

            // Notification pour informer l'utilisateur
            Notification::make()
                ->title('Aperçu des catégories')
                ->body('Veuillez vérifier les catégories à importer et confirmer l\'importation')
                ->success()
                ->send();

            // Forcer le rafraîchissement de la page pour résoudre le problème du modal
            // $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            // Désactiver le modal d'importation
            $this->isImporting = false;

            // Supprimer le fichier temporaire en cas d'erreur
            $this->cleanupTempFile($data['categories_file'][0]);

            Notification::make()
                ->title('Erreur')
                ->body('Erreur lors de l\'analyse des catégories: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function confirmImportCategories()
    {
        // Récupérer le chemin du fichier stocké en session
        $filePath = session('import_categories_file_path');

        if (!$filePath || !file_exists($filePath) || !is_readable($filePath)) {
            Notification::make()
                ->title('Erreur')
                ->body('Le fichier d\'importation n\'est plus disponible. Veuillez réessayer.')
                ->danger()
                ->send();
            return;
        }

        // Activer le modal d'importation
        $this->isImporting = true;
        $this->importMessage = "Importation des catégories en cours...";
        try {
            // Créer une instance de CategorieImport pour l'importation réelle
            $import = new CategorieImport();

            // Exécuter l'importation réelle
            $results = $import->importConfirmed($filePath);

            // Supprimer le fichier temporaire
            if (file_exists($filePath)) {
                unlink($filePath);
            }

            // Supprimer le chemin du fichier de la session
            session()->forget('import_categories_file_path');

            // Réinitialiser les propriétés d'aperçu
            $this->preview_categories = [];
            $this->preview_total = 0;
            $this->show_preview = false;

            // Réinitialiser la propriété publique
            $this->categories_file = [];
            $this->form->fill([
                'categories_file' => null,
            ]);

            // Préparer le message de notification
            $message = "Importation terminée avec succès.\n" .
                       "Total: {$results['total']}\n" .
                       "Créées: {$results['created']}\n" .
                       "Mises à jour: {$results['updated']}";

            // Ajouter les avertissements s'il y en a
            if (!empty($results['warnings'])) {
                $warningCount = count($results['warnings']);
                $warningMessage = "\n\nAvertissements: {$warningCount}";

                if ($warningCount > 0) {
                    $warningMessage .= "\n" . implode("\n", array_slice($results['warnings'], 0, 3));

                    if ($warningCount > 3) {
                        $warningMessage .= "\n... et " . ($warningCount - 3) . " autres avertissements";
                    }
                }

                $message .= $warningMessage;
            }

            // Ajouter les erreurs s'il y en a
            if (!empty($results['errors'])) {
                $errorCount = count($results['errors']);
                $errorMessage = "\n\nErreurs: {$errorCount}";

                if ($errorCount > 0) {
                    $errorMessage .= "\n" . implode("\n", array_slice($results['errors'], 0, 3));

                    if ($errorCount > 3) {
                        $errorMessage .= "\n... et " . ($errorCount - 3) . " autres erreurs";
                    }
                }

                $message .= $errorMessage;
            }

            // Désactiver le modal d'importation
            $this->isImporting = false;

            // Afficher la notification
            $notification = Notification::make()
                ->title('Importation terminée')
                ->body($message);

            if (!empty($results['errors'])) {
                $notification->warning();
            } else {
                $notification->success();
            }

            $notification->send();

            // Forcer le rafraîchissement de la page pour résoudre le problème du modal
            $this->redirect(request()->header('Referer'));

        } catch (\Exception $e) {
            // Désactiver le modal d'importation
            $this->isImporting = false;

            Notification::make()
                ->title('Erreur')
                ->body('Erreur lors de l\'importation des catégories: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function cancelImportCategories()
    {
        // Récupérer le chemin du fichier stocké en session
        $filePath = session('import_categories_file_path');

        // Supprimer le fichier temporaire s'il existe
        if ($filePath && file_exists($filePath)) {
            unlink($filePath);
        }

        // Supprimer le chemin du fichier de la session
        session()->forget('import_categories_file_path');

        // Réinitialiser les propriétés d'aperçu
        $this->preview_categories = [];
        $this->preview_total = 0;
        $this->show_preview = false;

        // Réinitialiser la propriété publique
        $this->categories_file = [];
        $this->form->fill([
            'categories_file' => null,
        ]);

        Notification::make()
            ->title('Importation annulée')
            ->body('L\'importation des catégories a été annulée.')
            ->info()
            ->send();
    }

    public function importProducts()
    {
        // Récupérer les données du formulaire
        $data = $this->form->getState();

        if (empty($data['products_file'])) {
            Notification::make()
                ->title('Erreur')
                ->body('Veuillez sélectionner un fichier à importer')
                ->danger()
                ->send();
            return;
        }

        // Activer le modal d'importation
        $this->isImporting = true;
        $this->importMessage = "Importation des produits en cours...";

        $filePath = $this->getFilePath($data['products_file']);
        $marchandId = $data['marchand_id'] ?? null;
        $import = new ProduitImport($marchandId);

        try {
            $import->import($filePath);
            $results = $import->getResults();

            // Supprimer le fichier temporaire
            $this->cleanupTempFile($data['products_file'][0]);

            // Désactiver le modal d'importation
            $this->isImporting = false;

            // Afficher les résultats
            $this->showImportResults('Produits', $results);

            // Forcer le rafraîchissement de la page pour résoudre le problème du modal
            $this->redirect(request()->header('Referer'));

            // Réinitialiser les propriétés publiques
            $this->products_file = [];
            $this->marchand_id = null;
            $this->form->fill([
                'products_file' => null,
                'marchand_id' => null,
            ]);
        } catch (\Exception $e) {
            // Désactiver le modal d'importation
            $this->isImporting = false;

            Notification::make()
                ->title('Erreur')
                ->body('Erreur lors de l\'importation des produits: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Obtient le chemin complet du fichier téléchargé
     */
    protected function getFilePath($fileKey)
    {
        try {
            // Créer le répertoire d'importation s'il n'existe pas
            $importDir = storage_path('app/imports');
            if (!is_dir($importDir)) {
                mkdir($importDir, 0755, true);
            }

            // Nom de fichier unique pour éviter les collisions
            $uniqueFilename = uniqid('import_') . '_' . basename($fileKey);
            $newPath = $importDir . '/' . $uniqueFilename;

            // Essayer différents chemins possibles pour le fichier source
            $possiblePaths = [
                Storage::disk('local')->path('public/' . $fileKey),
                storage_path('app/livewire-tmp/' . $fileKey),
                storage_path('app/livewire-tmp/' . basename($fileKey)),
                storage_path('app/public/' . $fileKey),
                public_path('storage/' . $fileKey),
            ];

            foreach ($possiblePaths as $sourcePath) {
                if (file_exists($sourcePath) && is_readable($sourcePath)) {
                    // Copier le fichier vers notre emplacement sécurisé
                    if (copy($sourcePath, $newPath)) {
                        return $newPath;
                    }
                }
            }

            // Si le fichier est une URL encodée en base64 (cas de certains fichiers Livewire)
            if (strpos($fileKey, '-meta') !== false) {
                // Extraire le nom de fichier original
                $parts = explode('-meta', $fileKey);
                $encodedName = $parts[0];

                // Chercher dans le répertoire livewire-tmp pour des fichiers correspondants
                $livewireTmpDir = storage_path('app/livewire-tmp');
                if (is_dir($livewireTmpDir)) {
                    $files = scandir($livewireTmpDir);
                    foreach ($files as $file) {
                        if (strpos($file, $encodedName) === 0) {
                            $sourcePath = $livewireTmpDir . '/' . $file;
                            if (file_exists($sourcePath) && is_readable($sourcePath)) {
                                if (copy($sourcePath, $newPath)) {
                                    return $newPath;
                                }
                            }
                        }
                    }
                }
            }

            // Si nous arrivons ici, nous n'avons pas pu trouver ou copier le fichier
            throw new \Exception("Impossible de trouver ou d'accéder au fichier téléchargé. Veuillez réessayer.");
        } catch (\Exception $e) {
            throw new \Exception("Erreur lors de la récupération du fichier: " . $e->getMessage());
        }
    }

    /**
     * Nettoie le fichier temporaire après utilisation
     */
    protected function cleanupTempFile($fileKey)
    {
        try {
            // Supprimer le fichier du disque local
            if (!empty($fileKey)) {
                // Chemins possibles pour les fichiers temporaires
                $possiblePaths = [
                    Storage::disk('local')->path('public/' . $fileKey),
                    storage_path('app/livewire-tmp/' . $fileKey),
                    storage_path('app/livewire-tmp/' . basename($fileKey)),
                    storage_path('app/public/' . $fileKey),
                    public_path('storage/' . $fileKey),
                ];

                // Supprimer tous les fichiers temporaires possibles
                foreach ($possiblePaths as $path) {
                    if (file_exists($path)) {
                        @unlink($path);
                    }
                }

                // Supprimer les fichiers dans le répertoire imports
                $importDir = storage_path('app/imports');
                if (is_dir($importDir)) {
                    $files = scandir($importDir);
                    foreach ($files as $file) {
                        if ($file !== '.' && $file !== '..' && strpos($file, basename($fileKey)) !== false) {
                            @unlink($importDir . '/' . $file);
                        }
                    }
                }

                // Si le fichier est une URL encodée en base64 (cas de certains fichiers Livewire)
                if (strpos($fileKey, '-meta') !== false) {
                    $parts = explode('-meta', $fileKey);
                    $encodedName = $parts[0];

                    // Chercher et supprimer les fichiers correspondants dans livewire-tmp
                    $livewireTmpDir = storage_path('app/livewire-tmp');
                    if (is_dir($livewireTmpDir)) {
                        $files = scandir($livewireTmpDir);
                        foreach ($files as $file) {
                            if (strpos($file, $encodedName) === 0) {
                                @unlink($livewireTmpDir . '/' . $file);
                            }
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            // Journaliser l'erreur mais ne pas interrompre le processus
            \Illuminate\Support\Facades\Log::error("Erreur lors de la suppression du fichier temporaire: " . $e->getMessage());
        }
    }

    protected function showImportResults(string $type, array $results)
    {
        $notification = Notification::make()
            ->title('Importation terminée')
            ->body("Importation des {$type} terminée avec succès.\n" .
                   "Total: {$results['total']}\n" .
                   "Créés: {$results['created']}\n" .
                   "Mis à jour: {$results['updated']}");

        if (!empty($results['errors'])) {
            $errorCount = count($results['errors']);
            $errorMessage = "Erreurs: {$errorCount}";

            if ($errorCount > 0) {
                $errorMessage .= "\n" . implode("\n", array_slice($results['errors'], 0, 5));

                if ($errorCount > 5) {
                    $errorMessage .= "\n... et " . ($errorCount - 5) . " autres erreurs";
                }
            }

            $notification->warning();
            $notification->body($notification->getBody() . "\n\n" . $errorMessage);
        } else {
            $notification->success();
        }

        $notification->send();
    }

    public function downloadCategoriesTemplate()
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="categories_template.csv"',
        ];

        $callback = function() {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['nom_fr', 'nom_en', 'description_fr', 'description_en', 'categorie_parent', 'image_url']);

            // Exemple de données avec traductions
            fputcsv($file, ['Électronique', 'Electronics', 'Produits électroniques', 'Electronic products', '', 'categories/electronics.jpg']);
            fputcsv($file, ['Ordinateurs', 'Computers', 'Ordinateurs et accessoires', 'Computers and accessories', 'Électronique', 'categories/computers.jpg']);
            fputcsv($file, ['Smartphones', 'Smartphones', 'Téléphones intelligents', 'Smartphones and accessories', 'Électronique', 'categories/smartphones.jpg']);
            fputcsv($file, ['Accessoires', 'Accessories', 'Accessoires pour appareils électroniques', 'Accessories for electronic devices', 'Électronique', 'categories/accessories.jpg']);
            fputcsv($file, ['Vêtements', 'Clothing', 'Vêtements et mode', 'Clothing and fashion', '', 'categories/clothing.jpg']);
            fputcsv($file, ['Hommes', 'Men', 'Vêtements pour hommes', 'Men\'s clothing', 'Vêtements', 'categories/men.jpg']);
            fputcsv($file, ['Femmes', 'Women', 'Vêtements pour femmes', 'Women\'s clothing', 'Vêtements', 'categories/women.jpg']);

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function downloadProductsTemplate()
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="products_template.csv"',
        ];

        $callback = function() {
            $file = fopen('php://output', 'w');
            fputcsv($file, [
                'nom_fr', 'nom_en', 'description_fr', 'description_en', 'categorie',
                'prix', 'currency', 'stock', 'prix_remise', 'date_debut_remise', 'date_fin_remise',
                'poids', 'longueur', 'largeur', 'hauteur'
            ]);

            // Exemples de données avec traductions
            fputcsv($file, [
                'Ordinateur portable XYZ', 'XYZ Laptop', 'Ordinateur portable haute performance avec écran 15.6"', 'High-performance laptop with 15.6" screen', 'Ordinateurs',
                '999.99', 'FCFA', '10', '899.99', '2023-01-01', '2023-12-31',
                '2.5', '35', '25', '2'
            ]);

            fputcsv($file, [
                'Smartphone ABC', 'ABC Smartphone', 'Smartphone avec écran AMOLED et appareil photo 48MP', 'Smartphone with AMOLED display and 48MP camera', 'Smartphones',
                '599.99', 'EUR', '20', '', '', '',
                '0.2', '15', '7', '1'
            ]);

            fputcsv($file, [
                'Casque sans fil Pro', 'Pro Wireless Headphones', 'Casque sans fil avec réduction de bruit active', 'Wireless headphones with active noise cancellation', 'Accessoires',
                '199.99', 'USD', '15', '149.99', '2023-02-01', '2023-03-31',
                '0.3', '18', '16', '8'
            ]);

            fputcsv($file, [
                'Montre connectée', 'Smartwatch', 'Montre connectée avec suivi de la santé et notifications', 'Smartwatch with health tracking and notifications', 'Accessoires',
                '249.99', 'XOF', '8', '', '', '',
                '0.05', '4.5', '4.5', '1.2'
            ]);

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
