# Guide d'implémentation du CDN pour Lorrelei Marketplace

Ce guide détaille l'implémentation d'un système CDN (Content Delivery Network) pour les fichiers statiques de Lorrelei Marketplace, en particulier les images des produits, catégories, bannières et autres ressources statiques, avec une attention particulière à la gestion des miniatures.

## Table des matières

1. [Architecture actuelle](#architecture-actuelle)
2. [Architecture CDN proposée](#architecture-cdn-proposée)
3. [Gestion des miniatures et formats d'images](#gestion-des-miniatures-et-formats-dimages)
4. [Configuration du système de stockage](#configuration-du-système-de-stockage)
5. [Adaptation du système d'upload d'images](#adaptation-du-système-dupload-dimages)
6. [Gestion des URLs dans le frontend](#gestion-des-urls-dans-le-frontend)
7. [Migration des fichiers existants](#migration-des-fichiers-existants)
8. [Configuration pour l'environnement de production](#configuration-pour-lenvironnement-de-production)

## Architecture actuelle

### Structure de stockage des images

Actuellement, les images sont stockées dans le dossier `public/images/` avec la structure suivante :

- **Produits** : `public/images/products/{folder_prefix}/{product_id}/...`
  - Images principales : directement dans le dossier du produit
  - Images de variantes : `variants/{variant_id}/...`
  - Images de couleurs : `colors/...`

- **Catégories** : `public/images/categories/{folder_prefix}/...`

- **Bannières** : `public/images/banners/{folder_prefix}/...`

- **Autres ressources** : `public/images/{resource_type}/...`

Le `folder_prefix` est déterminé par la méthode `ImageStorage::getFolderPrefix()` qui utilise les premiers chiffres de l'ID pour créer une structure de dossiers hiérarchique (par exemple, pour l'ID 1234, le préfixe est "1").

### Gestion des uploads

Les uploads sont gérés principalement via Filament Admin Panel, en utilisant :

1. Le trait `HandlesImageStorage` qui configure les composants `FileUpload`
2. La classe `ImageStorage` qui gère le stockage des fichiers avec la structure de dossiers basée sur l'ID
3. Le disque `public_images` configuré dans `config/filesystems.php`

### Affichage des images

Dans le frontend React, les images sont affichées via :

1. Des composants comme `CardProduit`, `Banner`, `ListeCategories`, etc.
2. Des URLs relatives comme `/images/products/1/123/image.jpg`
3. Des préchargements d'images pour améliorer l'expérience utilisateur

## Architecture CDN proposée

### Objectifs

1. Servir les fichiers statiques depuis un sous-domaine dédié (`cdn.Lorrelei.com`)
2. Conserver la même structure de dossiers et la logique de nommage
3. Permettre un développement local sans CDN
4. Faciliter la migration vers un vrai CDN externe si nécessaire à l'avenir
5. Gérer efficacement les miniatures pour différents contextes d'affichage

### Structure proposée

```
┌─────────────────────┐     ┌─────────────────────┐
│ Application         │     │ CDN                 │
│ Lorrelei.com         │     │ cdn.Lorrelei.com     │
├─────────────────────┤     ├─────────────────────┤
│ - Logique métier    │     │ - Images produits   │
│ - API               │     │ - Images catégories │
│ - Interface admin   │     │ - Bannières         │
│ - Frontend React    │     │ - CSS/JS statiques  │
└─────────────────────┘     └─────────────────────┘
```

## Gestion des miniatures et formats d'images

### Contextes d'utilisation des images

Différents contextes nécessitent différentes tailles d'images :

1. **Cartes produits** (`CardProduit.tsx`)
   - Miniatures carrées (aspect-ratio 1:1)
   - Taille optimale : 300x300px
   - Utilisées sur : page d'accueil, listings de catégories, résultats de recherche

2. **Page produit** (`product.tsx`)
   - Images principales : grande taille, haute qualité (800x800px ou plus)
   - Miniatures pour la galerie : 100x100px
   - Effet de zoom : image haute résolution (1200x1200px ou plus)

3. **Cartes catégories** (`ListeCategories.tsx`)
   - Miniatures carrées ou rectangulaires selon le variant
   - Taille optimale : 200x200px (carré) ou 300x150px (rectangle)

4. **Bannières** (`Banner.tsx`)
   - Format large (aspect-ratio 21:9)
   - Taille optimale : 1200x514px

5. **Variantes de produits**
   - Miniatures pour sélection : 50x50px
   - Images détaillées : 600x600px

### Stratégie de génération des miniatures

Pour chaque image uploadée, nous générerons automatiquement plusieurs formats :

| Format      | Taille      | Utilisation                                  |
|-------------|-------------|----------------------------------------------|
| `original`  | Originale   | Stockage, zoom                               |
| `large`     | 800x800     | Image principale sur la page produit         |
| `medium`    | 400x400     | Cartes produits, listings                    |
| `small`     | 200x200     | Miniatures, sélecteurs de variantes          |
| `thumbnail` | 100x100     | Galerie d'images, administration             |

Pour les bannières :

| Format      | Taille      | Utilisation                                  |
|-------------|-------------|----------------------------------------------|
| `original`  | Originale   | Stockage                                     |
| `desktop`   | 1200x514    | Affichage sur desktop                        |
| `tablet`    | 768x329     | Affichage sur tablette                       |
| `mobile`    | 375x161     | Affichage sur mobile                         |

### Structure de stockage des miniatures

Les miniatures seront stockées dans des sous-dossiers selon leur format :

```
products/{folder_prefix}/{product_id}/
  ├── original/image1.jpg
  ├── large/image1.jpg
  ├── medium/image1.jpg
  ├── small/image1.jpg
  └── thumbnail/image1.jpg
```

## Configuration du système de stockage

### 1. Modification du fichier `config/filesystems.php`

```php
'disks' => [
    // Disques existants...

    'public_images' => [
        'driver' => 'local',
        'root' => public_path('images'),
        'url' => env('CDN_URL', env('APP_URL').'/images'),
        'visibility' => 'public',
        'throw' => false,
    ],

    // Nouveau disque pour le CDN en production
    'cdn' => [
        'driver' => env('CDN_DRIVER', 'local'),
        'root' => env('CDN_DRIVER') === 'local' ? public_path('images') : storage_path('app/cdn'),
        'url' => env('CDN_URL', env('APP_URL').'/images'),
        'visibility' => 'public',
        'throw' => false,
    ],
],
```

### 2. Ajout des variables d'environnement

Ajoutez ces variables à votre fichier `.env` :

```
CDN_URL=http://localhost:8000/images
CDN_DRIVER=local
CDN_ENABLED=false
```

En production, ces variables seront modifiées :

```
CDN_URL=https://cdn.Lorrelei.com
CDN_DRIVER=local
CDN_ENABLED=true
```

### 3. Création d'un service CDN avec gestion des miniatures

Créez un nouveau service pour gérer les opérations CDN et les miniatures :

```php
// app/Services/CdnService.php
namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;
use App\Helpers\ImageStorage;
use Intervention\Image\Facades\Image;

class CdnService
{
    /**
     * Formats d'images disponibles avec leurs dimensions
     */
    protected $imageFormats = [
        'original' => null, // Taille originale
        'large' => [800, 800],
        'medium' => [400, 400],
        'small' => [200, 200],
        'thumbnail' => [100, 100],
    ];

    /**
     * Formats spécifiques pour les bannières
     */
    protected $bannerFormats = [
        'original' => null,
        'desktop' => [1200, 514],
        'tablet' => [768, 329],
        'mobile' => [375, 161],
    ];

    /**
     * Détermine si le CDN est activé
     */
    public function isEnabled(): bool
    {
        return config('filesystems.cdn_enabled', false);
    }

    /**
     * Obtient le disque de stockage approprié
     */
    public function getDisk()
    {
        return Storage::disk($this->isEnabled() ? 'cdn' : 'public_images');
    }

    /**
     * Retourne les formats d'images disponibles
     */
    public function getImageFormats()
    {
        return $this->imageFormats;
    }

    /**
     * Retourne les formats de bannières disponibles
     */
    public function getBannerFormats()
    {
        return $this->bannerFormats;
    }

    /**
     * Génère l'URL complète pour un chemin d'image
     */
    public function url(string $path, string $format = 'original'): string
    {
        if ($format === 'original') {
            return $this->getDisk()->url($path);
        }

        // Construire le chemin pour le format spécifié
        $pathInfo = pathinfo($path);
        $formatPath = $pathInfo['dirname'] . '/' . $format . '/' . $pathInfo['basename'];

        // Vérifier si le format existe, sinon revenir à l'original
        if ($this->getDisk()->exists($formatPath)) {
            return $this->getDisk()->url($formatPath);
        }

        return $this->getDisk()->url($path);
    }

    /**
     * Stocke une image et génère ses miniatures
     */
    public function storeImageWithThumbnails(UploadedFile $file, $id, string $baseDir, bool $isBanner = false): array
    {
        $folderPrefix = ImageStorage::getFolderPrefix($id);
        $basePath = "{$baseDir}/{$folderPrefix}";

        // Générer un nom de fichier unique
        $filename = $file->hashName();

        // Déterminer les formats à générer
        $formats = $isBanner ? $this->bannerFormats : $this->imageFormats;
        $results = [];

        foreach ($formats as $format => $dimensions) {
            // Créer le dossier pour ce format
            $formatPath = "{$basePath}/{$format}";

            if ($format === 'original') {
                // Stocker l'image originale
                $this->getDisk()->putFileAs($formatPath, $file, $filename);
                $results[$format] = "{$formatPath}/{$filename}";
            } else {
                // Générer la miniature
                $img = Image::make($file->getRealPath());

                // Redimensionner l'image
                $img->resize($dimensions[0], $dimensions[1], function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

                // Stocker la miniature
                $this->getDisk()->put("{$formatPath}/{$filename}", $img->encode());
                $results[$format] = "{$formatPath}/{$filename}";
            }
        }

        return $results;
    }
}
```

### 4. Enregistrement du service dans un fournisseur

```php
// app/Providers/CdnServiceProvider.php
namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\CdnService;

class CdnServiceProvider extends ServiceProvider
{
    public function register()
    {
        $this->app->singleton(CdnService::class, function ($app) {
            return new CdnService();
        });

        // Ajouter la configuration CDN_ENABLED
        config(['filesystems.cdn_enabled' => env('CDN_ENABLED', false)]);
    }
}
```

Ajoutez ce fournisseur à votre fichier `config/app.php` :

```php
'providers' => [
    // Autres fournisseurs...
    App\Providers\CdnServiceProvider::class,
],
```

## Adaptation du système d'upload d'images

### 1. Modification du trait HandlesImageStorage

```php
// app/Filament/Traits/HandlesImageStorage.php
use App\Services\CdnService;
use Intervention\Image\Facades\Image;

trait HandlesImageStorage
{
    /**
     * Configure un composant FileUpload pour utiliser notre système de stockage basé sur l'ID
     */
    public static function configureImageUpload(FileUpload $component, string $baseDir, bool $isBanner = false): FileUpload
    {
        $cdnService = app(CdnService::class);
        $diskName = $cdnService->isEnabled() ? 'cdn' : 'public_images';

        return $component
            ->disk($diskName)
            ->visibility('public')
            ->directory($baseDir)
            ->saveUploadedFileUsing(function ($file, $record) use ($baseDir, $cdnService, $isBanner) {
                if (!$record || !$record->exists) {
                    // Si le record n'existe pas encore, stocker temporairement dans le dossier '0'
                    $tempPath = "{$baseDir}/0/original";
                    return $file->store($tempPath, $cdnService->getDisk()->getDriver());
                }

                // Stocker l'image avec ses miniatures
                $paths = $cdnService->storeImageWithThumbnails($file, $record->id, $baseDir, $isBanner);

                // Retourner le chemin de l'image originale
                return $paths['original'];
            });
    }

    // Autres méthodes...
}
```

### 2. Modification de la classe ImageStorage

```php
// app/Helpers/ImageStorage.php
use App\Services\CdnService;

class ImageStorage
{
    // Méthode getFolderPrefix inchangée...

    /**
     * Stocke une image dans le dossier approprié
     */
    public static function storeImage(UploadedFile $file, $id, string $baseDir): string
    {
        $cdnService = app(CdnService::class);
        return $cdnService->storeImage($file, $id, $baseDir);
    }

    /**
     * Génère l'URL complète pour une image
     */
    public static function getImageUrl(string $path): string
    {
        $cdnService = app(CdnService::class);
        return $cdnService->url($path);
    }
}
```

## Gestion des URLs dans le frontend

### 1. Création d'un helper pour les URLs CDN

```php
// app/helpers.php
if (!function_exists('cdn_url')) {
    /**
     * Génère une URL CDN pour un fichier
     *
     * @param string $path Chemin du fichier
     * @return string URL complète
     */
    function cdn_url(string $path): string
    {
        return app(App\Services\CdnService::class)->url($path);
    }
}
```

Assurez-vous que ce fichier est chargé en ajoutant ceci à votre `composer.json` :

```json
"autoload": {
    "files": [
        "app/helpers.php"
    ]
}
```

Puis exécutez `composer dump-autoload`.

### 2. Modification des modèles pour utiliser le CDN

```php
// app/Models/Produit.php
public function getImageUrlAttribute()
{
    if (!$this->images || empty($this->images)) {
        return null;
    }

    $firstImage = is_array($this->images) ? $this->images[0] : $this->images;
    return cdn_url($firstImage);
}

// Autres méthodes pour les URLs d'images...
```

### 3. Adaptation du contexte React

Créez un hook React pour gérer les URLs CDN avec gestion des formats :

```tsx
// resources/js/hooks/use-cdn.ts
import { useMemo } from 'react';

export type ImageFormat = 'original' | 'large' | 'medium' | 'small' | 'thumbnail';
export type BannerFormat = 'original' | 'desktop' | 'tablet' | 'mobile';

export function useCdn() {
  // La base URL du CDN est injectée par le serveur via window.__CDN_URL__
  const cdnBaseUrl = useMemo(() => {
    return (window as any).__CDN_URL__ || '/images';
  }, []);

  /**
   * Convertit un chemin d'image relatif en URL CDN complète
   */
  const cdnUrl = (path: string, format: ImageFormat = 'original'): string => {
    if (!path) return '';

    // Si le chemin est déjà une URL complète, la retourner telle quelle
    if (path.startsWith('http://') || path.startsWith('https://')) {
      return path;
    }

    // Si le format est original, retourner l'URL sans modification de chemin
    if (format === 'original') {
      // Si le chemin commence par '/images/', remplacer par la base URL du CDN
      if (path.startsWith('/images/')) {
        return `${cdnBaseUrl}${path.substring(7)}`;
      }

      // Sinon, concaténer la base URL et le chemin
      return `${cdnBaseUrl}/${path}`;
    }

    // Pour les autres formats, modifier le chemin pour inclure le format
    const pathParts = path.split('/');
    const filename = pathParts.pop();

    // Construire le nouveau chemin avec le format
    const newPath = [...pathParts, format, filename].join('/');

    // Appliquer la base URL
    if (newPath.startsWith('/images/')) {
      return `${cdnBaseUrl}${newPath.substring(7)}`;
    }

    return `${cdnBaseUrl}/${newPath}`;
  };

  /**
   * Génère l'URL d'une bannière dans le format approprié
   */
  const bannerUrl = (path: string, format: BannerFormat = 'desktop'): string => {
    return cdnUrl(path, format as ImageFormat);
  };

  /**
   * Détermine automatiquement le format d'image à utiliser en fonction de la largeur de l'écran
   */
  const responsiveImageUrl = (path: string): string => {
    // Utiliser matchMedia pour déterminer la taille de l'écran
    const isMobile = window.matchMedia('(max-width: 640px)').matches;
    const isTablet = window.matchMedia('(max-width: 1024px)').matches;

    if (isMobile) {
      return cdnUrl(path, 'small');
    } else if (isTablet) {
      return cdnUrl(path, 'medium');
    } else {
      return cdnUrl(path, 'large');
    }
  };

  return { cdnUrl, bannerUrl, responsiveImageUrl };
}
```

### 4. Injection de la configuration CDN dans le frontend

Modifiez le middleware HandleInertiaRequests :

```php
// app/Http/Middleware/HandleInertiaRequests.php
public function share(Request $request): array
{
    return array_merge(parent::share($request), [
        'auth' => [
            'user' => $request->user(),
        ],
        'cdn' => [
            'url' => config('filesystems.disks.cdn.url'),
            'enabled' => config('filesystems.cdn_enabled', false),
        ],
        // Autres données partagées...
    ]);
}
```

Puis ajoutez un script dans votre template principal :

```blade
<!-- resources/views/app.blade.php -->
<script>
    window.__CDN_URL__ = "{{ config('filesystems.disks.cdn.url') }}";
</script>
```

### 5. Utilisation dans les composants React

#### CardProduit.tsx (miniatures pour les listings)

```tsx
// resources/js/components/ecommerce/CardProduit.tsx
import { useCdn } from '@/hooks/use-cdn';

export default function CardProduit({ product, className = '' }: CardProduitProps) {
  const { cdnUrl } = useCdn();

  // Reste du code...

  return (
    <Card className={`overflow-hidden transition-all hover:shadow-md ${className}`}>
      <Link href={route('product', { productSlug: product.slug })} className="block">
        <div className="relative aspect-square overflow-hidden">
          {product.mainImageUrls.length > 0 && (
            <>
              <img
                src={cdnUrl(product.mainImageUrls[0], 'medium')}
                alt={product.name}
                className="absolute inset-0 h-full w-full object-cover"
              />
              {/* Image secondaire pour l'effet de survol */}
              {product.mainImageUrls.length > 1 && (
                <img
                  src={cdnUrl(product.mainImageUrls[1], 'medium')}
                  alt={`${product.name} - Image secondaire`}
                  className={`absolute inset-0 h-full w-full object-cover transition-opacity duration-300 ${
                    isHovered ? 'opacity-100' : 'opacity-0'
                  }`}
                />
              )}
            </>
          )}
        </div>
      </Link>
    </Card>
  );
}
```

#### Page produit (images principales et miniatures)

```tsx
// resources/js/pages/ecommerce/product.tsx
import { useCdn } from '@/hooks/use-cdn';

export default function ProductPage() {
  const { cdnUrl } = useCdn();

  // Reste du code...

  return (
    <div className="product-page">
      {/* Image principale */}
      <div className="product-image">
        <img
          src={cdnUrl(selectedImage, 'large')}
          alt={product.name}
          className="h-full w-full cursor-pointer object-cover"
          onClick={goToNextImage}
        />
      </div>

      {/* Galerie de miniatures */}
      <div className="product-thumbnails">
        {product.mainImageUrls.map((image, index) => (
          <button
            key={`main-${index}`}
            className={`relative h-16 w-16 overflow-hidden rounded-md border ${
              selectedImage === image ? 'border-primary' : ''
            }`}
            onClick={() => setSelectedImage(image)}
          >
            <img
              src={cdnUrl(image, 'thumbnail')}
              alt={`${product.name} - Image ${index + 1}`}
              className="h-full w-full object-cover"
            />
          </button>
        ))}
      </div>
    </div>
  );
}
```

#### Bannières (formats responsifs)

```tsx
// resources/js/components/ecommerce/Banner.tsx
import { useCdn } from '@/hooks/use-cdn';

export default function Banner({ banner, className = '' }: BannerProps) {
  const { bannerUrl } = useCdn();

  // Reste du code...

  return (
    <>
      {banner.fullImageUrl ? (
        <Link href={banner.targetUrl || '#'} className="block relative aspect-[21/9] w-full">
          <picture>
            <source media="(max-width: 640px)" srcSet={bannerUrl(banner.fullImageUrl, 'mobile')} />
            <source media="(max-width: 1024px)" srcSet={bannerUrl(banner.fullImageUrl, 'tablet')} />
            <img
              src={bannerUrl(banner.fullImageUrl, 'desktop')}
              alt={banner.title || 'Banner image'}
              className="absolute inset-0 h-full w-full object-cover"
            />
          </picture>
        </Link>
      ) : (
        // Reste du code pour les bannières sans image...
      )}
    </>
  );
}
```

## Migration des fichiers existants

### 1. Création d'une commande Artisan pour la migration et génération des miniatures

```php
// app/Console/Commands/MigrateImagesToCdn.php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Services\CdnService;
use Intervention\Image\Facades\Image;

class MigrateImagesToCdn extends Command
{
    protected $signature = 'cdn:migrate {--force : Écraser les fichiers existants} {--generate-thumbnails : Générer les miniatures}';
    protected $description = 'Migre les images existantes vers le CDN et génère les miniatures';

    protected $cdnService;

    public function __construct(CdnService $cdnService)
    {
        parent::__construct();
        $this->cdnService = $cdnService;
    }

    public function handle()
    {
        if (!$this->cdnService->isEnabled()) {
            $this->error('Le CDN n\'est pas activé. Veuillez activer le CDN dans votre fichier .env (CDN_ENABLED=true).');
            return 1;
        }

        $this->info('Début de la migration des images vers le CDN...');

        $force = $this->option('force');
        $generateThumbnails = $this->option('generate-thumbnails');
        $sourceDisk = Storage::disk('public_images');
        $targetDisk = $this->cdnService->getDisk();

        // Si les disques sont identiques et qu'on ne génère pas de miniatures, informer l'utilisateur
        if ($sourceDisk->getDriver() === $targetDisk->getDriver() && !$generateThumbnails) {
            $this->info('Les disques source et cible sont identiques. Aucune migration nécessaire.');
            return 0;
        }

        // Récupérer tous les fichiers
        $files = $sourceDisk->allFiles();
        $bar = $this->output->createProgressBar(count($files));

        $this->info(sprintf('Migration de %d fichiers...', count($files)));
        $bar->start();

        $copied = 0;
        $thumbnailsGenerated = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($files as $file) {
            try {
                // Ignorer les fichiers qui ne sont pas des images
                $extension = pathinfo($file, PATHINFO_EXTENSION);
                if (!in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    $skipped++;
                    $bar->advance();
                    continue;
                }

                // Déterminer si c'est une bannière
                $isBanner = strpos($file, 'banners/') === 0;

                // Copier le fichier original
                $originalDir = dirname($file) . '/original';
                $originalFile = $originalDir . '/' . basename($file);

                // Créer le dossier original s'il n'existe pas
                if (!$targetDisk->exists($originalDir)) {
                    $targetDisk->makeDirectory($originalDir);
                }

                // Copier le fichier original
                if (!$targetDisk->exists($originalFile) || $force) {
                    $contents = $sourceDisk->get($file);
                    $targetDisk->put($originalFile, $contents);
                    $copied++;
                }

                // Générer les miniatures si demandé
                if ($generateThumbnails) {
                    $formats = $isBanner ? $this->cdnService->getBannerFormats() : $this->cdnService->getImageFormats();

                    foreach ($formats as $format => $dimensions) {
                        if ($format === 'original') continue;

                        $formatDir = dirname($file) . '/' . $format;
                        $formatFile = $formatDir . '/' . basename($file);

                        // Créer le dossier pour ce format s'il n'existe pas
                        if (!$targetDisk->exists($formatDir)) {
                            $targetDisk->makeDirectory($formatDir);
                        }

                        // Générer et stocker la miniature si elle n'existe pas ou si force est activé
                        if (!$targetDisk->exists($formatFile) || $force) {
                            $img = Image::make($sourceDisk->path($file));

                            // Redimensionner l'image
                            $img->resize($dimensions[0], $dimensions[1], function ($constraint) {
                                $constraint->aspectRatio();
                                $constraint->upsize();
                            });

                            // Stocker la miniature
                            $targetDisk->put($formatFile, $img->encode());
                            $thumbnailsGenerated++;
                        }
                    }
                }
            } catch (\Exception $e) {
                $this->error(sprintf('Erreur lors du traitement du fichier %s: %s', $file, $e->getMessage()));
                $errors++;
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine(2);

        $this->info(sprintf(
            'Migration terminée: %d fichiers copiés, %d miniatures générées, %d ignorés, %d erreurs.',
            $copied,
            $thumbnailsGenerated,
            $skipped,
            $errors
        ));

        return 0;
    }
}
```

## Configuration pour l'environnement de production

### 1. Configuration du serveur web

Pour Apache, ajoutez un VirtualHost pour le sous-domaine CDN :

```apache
<VirtualHost *:80>
    ServerName cdn.Lorrelei.com
    DocumentRoot /chemin/vers/Lorrelei/public/images

    <Directory /chemin/vers/Lorrelei/public/images>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted

        # Cache headers
        <IfModule mod_expires.c>
            ExpiresActive On
            ExpiresByType image/jpg "access plus 1 year"
            ExpiresByType image/jpeg "access plus 1 year"
            ExpiresByType image/gif "access plus 1 year"
            ExpiresByType image/png "access plus 1 year"
            ExpiresByType image/webp "access plus 1 year"
            ExpiresByType image/svg+xml "access plus 1 year"
        </IfModule>

        # Compression
        <IfModule mod_deflate.c>
            AddOutputFilterByType DEFLATE image/svg+xml
        </IfModule>
    </Directory>

    ErrorLog ${APACHE_LOG_DIR}/cdn.Lorrelei.com-error.log
    CustomLog ${APACHE_LOG_DIR}/cdn.Lorrelei.com-access.log combined
</VirtualHost>
```

Pour Nginx :

```nginx
server {
    listen 80;
    server_name cdn.Lorrelei.com;
    root /chemin/vers/Lorrelei/public/images;

    location / {
        try_files $uri $uri/ =404;

        # Cache headers
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";

        # Compression
        gzip on;
        gzip_types image/svg+xml;
    }

    access_log /var/log/nginx/cdn.Lorrelei.com-access.log;
    error_log /var/log/nginx/cdn.Lorrelei.com-error.log;
}
```

### 2. Configuration DNS et HTTPS

Ajoutez un enregistrement CNAME pour le sous-domaine CDN et configurez HTTPS avec Let's Encrypt :

```
# Ajouter l'enregistrement DNS
cdn.Lorrelei.com. IN CNAME Lorrelei.com.

# Configurer HTTPS avec Let's Encrypt
sudo certbot --nginx -d cdn.Lorrelei.com
```

### 3. Variables d'environnement en production

```
CDN_URL=https://cdn.Lorrelei.com
CDN_DRIVER=local
CDN_ENABLED=true
```

## Conclusion

Cette implémentation permet de :

1. Servir les fichiers statiques depuis un sous-domaine dédié
2. Générer et gérer automatiquement des miniatures pour différents contextes d'affichage
3. Optimiser les performances en servant des images de taille appropriée
4. Conserver la même structure de dossiers et la logique de nommage
5. Faciliter le développement local sans CDN
6. Préparer le terrain pour une migration future vers un CDN externe

En suivant ce guide, vous pourrez mettre en place un système CDN efficace pour votre marketplace, améliorant ainsi les performances et la scalabilité de votre application.
