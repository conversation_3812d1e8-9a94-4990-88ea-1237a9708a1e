import axios from 'axios';

/**
 * Interface pour une devise
 */
export interface Currency {
  id: number;
  code: string;
  name: string;
  symbol: string;
  is_default: boolean;
  is_active: boolean;
  exchange_rate: number;
}

/**
 * Service pour gérer les opérations liées aux devises
 */
export class CurrencyService {
  /**
   * Récupère toutes les devises actives
   * 
   * @returns Une promesse qui résout avec un tableau de devises
   */
  async getAllCurrencies(): Promise<Currency[]> {
    try {
      const response = await axios.get('/api/currencies');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des devises:', error);
      return [];
    }
  }

  /**
   * Récupère la devise actuelle
   * 
   * @returns Une promesse qui résout avec la devise actuelle
   */
  async getCurrentCurrency(): Promise<Currency | null> {
    try {
      const response = await axios.get('/api/currencies/current');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération de la devise actuelle:', error);
      return null;
    }
  }

  /**
   * Définit la devise actuelle
   * 
   * @param currencyCode - Code de la devise à définir comme actuelle
   * @returns Une promesse qui résout avec la nouvelle devise actuelle
   */
  async setCurrentCurrency(currencyCode: string): Promise<Currency | null> {
    try {
      const response = await axios.post('/api/currencies/current', { currency: currencyCode });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la définition de la devise actuelle:', error);
      return null;
    }
  }

  /**
   * Récupère la devise par défaut
   * 
   * @returns Une promesse qui résout avec la devise par défaut
   */
  async getDefaultCurrency(): Promise<Currency | null> {
    try {
      const response = await axios.get('/api/currencies/default');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération de la devise par défaut:', error);
      return null;
    }
  }

  /**
   * Convertit un prix d'une devise à une autre
   * 
   * @param price - Prix à convertir
   * @param fromCurrency - Code de la devise source
   * @param toCurrency - Code de la devise cible
   * @returns Une promesse qui résout avec le prix converti
   */
  async convertPrice(price: number, fromCurrency: string, toCurrency: string): Promise<number> {
    try {
      const response = await axios.post('/api/currencies/convert', {
        price,
        from: fromCurrency,
        to: toCurrency
      });
      return response.data.converted_price;
    } catch (error) {
      console.error('Erreur lors de la conversion du prix:', error);
      return price;
    }
  }

  /**
   * Formate un prix avec le symbole de la devise
   * 
   * @param price - Prix à formater
   * @param currency - Devise à utiliser pour le formatage
   * @returns Le prix formaté avec le symbole de la devise
   */
  formatPrice(price: number, currency: Currency): string {
    return `${price.toFixed(0)} ${currency.symbol}`;
  }
}
