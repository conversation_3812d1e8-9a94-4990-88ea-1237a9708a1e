<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Banner extends Model
{
    use HasFactory, HasUuids, HasTranslations;

    /**
     * Les attributs qui sont traduisibles.
     *
     * @var array
     */
    public $translatable = ['title', 'description', 'button_text'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'image_url',
        'target_url',
        'position',
        'title',
        'description',
        'button_text',
        'type',
        'start_date',
        'end_date',
        'is_active',
        'priorite',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['full_image_url'];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'start_date' => 'datetime',
            'end_date' => 'datetime',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the full URL for the banner image.
     *
     * @return string|null
     */
    public function getFullImageUrlAttribute(): ?string
    {
        if (empty($this->image_url)) {
            return null;
        }

        // Vérifier si l'image est déjà un chemin complet
        if (strpos($this->image_url, '/') !== false) {
            return url('/images/' . $this->image_url);
        }

        // Déterminer le dossier basé sur l'ID de la bannière
        // Comme Banner utilise des UUIDs, nous utilisons les 2 premiers caractères
        $folderPrefix = substr($this->id, 0, 2);
        return url("/images/banners/{$folderPrefix}/{$this->image_url}");
    }
}
