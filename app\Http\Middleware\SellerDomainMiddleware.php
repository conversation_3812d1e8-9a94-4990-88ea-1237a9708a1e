<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SellerDomainMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $currentDomain = $request->getHost();
        $currentPort = $request->getPort();
        
        // Ajouter le port si ce n'est pas 80 ou 443
        if (!in_array($currentPort, [80, 443])) {
            $currentDomain .= ':' . $currentPort;
        }

        // Obtenir le domaine seller configuré
        if (app()->environment('local')) {
            $sellerDomain = config('domains.development.seller');
        } else {
            $sellerDomain = config('domains.seller.domain');
        }

        // Vérifier si on est sur le domaine seller
        if ($currentDomain === $sellerDomain) {
            // On est sur le domaine seller, vérifier les permissions
            if (!auth()->check()) {
                return redirect()->route('login');
            }

            if (auth()->user()->role !== 'Marchand') {
                abort(403, 'Accès non autorisé à l\'espace marchand.');
            }
        } else {
            // On n'est pas sur le domaine seller, bloquer l'accès aux routes marchand
            if ($request->is('marchand/*') || $request->is('marchand')) {
                // Rediriger vers le domaine seller
                $url = $request->secure() ? 'https://' : 'http://';
                $url .= $sellerDomain . '/' . ltrim($request->getRequestUri(), '/');
                
                return redirect($url, 301);
            }
        }

        return $next($request);
    }
}
