import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useTranslation } from '@/hooks/use-translation';

interface PaginationLimitSelectorProps {
  value: number;
  onChange: (value: number) => void;
  allowedLimits?: number[];
  className?: string;
}

/**
 * Composant permettant de sélectionner le nombre d'éléments par page
 */
export function PaginationLimitSelector({
  value,
  onChange,
  allowedLimits = [10, 20, 50, 100],
  className = '',
}: PaginationLimitSelectorProps) {
  const { tDefault } = useTranslation();

  const handleValueChange = (newValue: string) => {
    onChange(parseInt(newValue, 10));
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-sm text-muted-foreground">
        {tDefault('pagination.items_per_page', 'Items per page')}:
      </span>
      <Select value={value.toString()} onValueChange={handleValueChange}>
        <SelectTrigger className="h-8 w-[70px]">
          <SelectValue placeholder={value.toString()} />
        </SelectTrigger>
        <SelectContent>
          {allowedLimits.map((limit) => (
            <SelectItem key={limit} value={limit.toString()}>
              {limit}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
