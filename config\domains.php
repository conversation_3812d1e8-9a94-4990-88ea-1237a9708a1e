<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Application Domains Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration des domaines pour les différentes parties de l'application
    |
    */

    'main' => [
        'domain' => env('MAIN_DOMAIN', 'lorelei.com'),
        'url' => env('MAIN_URL', 'https://lorelei.com'),
        'name' => 'Loʁelei Marketplace',
        'description' => 'Site principal de la marketplace',
    ],

    'admin' => [
        'domain' => env('ADMIN_DOMAIN', 'admin.lorelei.com'),
        'url' => env('ADMIN_URL', 'https://admin.lorelei.com'),
        'name' => 'Administration Loʁelei',
        'description' => 'Interface d\'administration',
        'middleware' => ['auth', 'role:Admin'],
    ],

    'seller' => [
        'domain' => env('SELLER_DOMAIN', 'seller.lorelei.com'),
        'url' => env('SELLER_URL', 'https://seller.lorelei.com'),
        'name' => 'Espace Marchand Loʁelei',
        'description' => 'Interface pour les marchands',
        'middleware' => ['auth', 'role:Marchand'],
    ],

    'cdn' => [
        'domain' => env('CDN_DOMAIN', 'cdn.lorelei.com'),
        'url' => env('CDN_URL', 'https://cdn.lorelei.com'),
        'name' => 'CDN Loʁelei',
        'description' => 'Serveur de fichiers statiques',
    ],

    'api' => [
        'domain' => env('API_DOMAIN', 'api.lorelei.com'),
        'url' => env('API_URL', 'https://api.lorelei.com'),
        'name' => 'API Loʁelei',
        'description' => 'API REST de la marketplace',
    ],

    /*
    |--------------------------------------------------------------------------
    | Domain Detection
    |--------------------------------------------------------------------------
    |
    | Configuration pour la détection automatique du domaine
    |
    */

    'detection' => [
        'enabled' => env('DOMAIN_DETECTION_ENABLED', true),
        'force_https' => env('FORCE_HTTPS', true),
        'redirect_www' => env('REDIRECT_WWW', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Development Domains
    |--------------------------------------------------------------------------
    |
    | Domaines pour l'environnement de développement
    |
    */

    'development' => [
        'main' => env('DEV_MAIN_DOMAIN', 'localhost:8000'),
        'admin' => env('DEV_ADMIN_DOMAIN', 'admin.localhost:8000'),
        'seller' => env('DEV_SELLER_DOMAIN', 'seller.localhost:8000'),
        'cdn' => env('DEV_CDN_DOMAIN', 'cdn.localhost:8000'),
        'api' => env('DEV_API_DOMAIN', 'api.localhost:8000'),
    ],
];
