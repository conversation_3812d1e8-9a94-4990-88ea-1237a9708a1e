<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use PayPalCheckoutSdk\Core\PayPalHttpClient;
use PayPalCheckoutSdk\Core\ProductionEnvironment;
use PayPalCheckoutSdk\Core\SandboxEnvironment;
use PayPalCheckoutSdk\Orders\OrdersCaptureRequest;
use PayPalCheckoutSdk\Orders\OrdersCreateRequest;

class PayementController extends Controller
{
    //
    private $client;

    public function __construct()
    {
        $clientId  = config('services.paypal.client_id');
        $clientSecret = config('services.paypal.client_secret');
        $environment = config('services.paypal.mode') == 'sandbox'
            ? new SandboxEnvironment($clientId, $clientSecret)
            : new ProductionEnvironment($clientId, $clientSecret);

        $this->client = new PayPalHttpClient($environment);
    }

    public function createOrder(Request $request)
    {
        $request->validate([
            'montant' => 'required|numeric|min:0.01',
            'methode' => 'required|string|in:card,paypal,orange,mtn',
            'currency' => 'required|string|in:EUR,USD,FCFA',
        ]);

        $paypalOrderRequest = new OrdersCreateRequest();
        $paypalOrderRequest->prefer('return=representation');

        // Convertir FCFA en EUR si nécessaire
        $amount = $request->montant;
        $currency = $request->currency;
        if ($currency === 'FCFA') {
            $amount = $amount * 0.0015; // Taux approximatif FCFA -> EUR
            $currency = 'EUR';
        }

        $paypalOrderRequest->body = [
            "intent" => "CAPTURE",
            "purchase_units" => [
                [
                    "amount" => [
                        "currency_code" => $currency,
                        "value" => number_format($amount, 2, '.', ''),
                    ],
                ],
            ],
            "application_context" => [
                "cancel_url" => route('payment.cancel'),
                "return_url" => route('payment.success'),
            ],
        ];

        try {
            $response = $this->client->execute($paypalOrderRequest);
            return response()->json([
                'success' => true,
                'data' => $response,
                'id' => $response->result->id,
                'message' => 'Order created successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function captureOrder(Request $request, $orderId)
    {
        //
        $captureRequest = new OrdersCaptureRequest($orderId);
        $captureRequest->prefer('return=representation');

        try {
            $response = $this->client->execute($captureRequest);
            $result = $response->result;
            // Save the transaction details to your database if needed
            // Example: Save to a Payment model
            // \App\Models\Payment::create([
            //     'user_id' => auth()->id(),
            //     'order_id' => $result->id,
            //     'amount' => $result->purchase_units[0]->amount->value,
            //     'currency' => $result->purchase_units[0]->amount->currency_code,
            //     'status' => $result->status,
            //     'transaction_id' => $result->purchase_units[0]->payments->captures[0]->id,
            // ]);
            return response()->json([
                'success' => true,
                'data' => $result,
                'transaction_id' => $result->purchase_units[0]->payments->captures[0]->id,
                'status' => $result->status,
                'message' => 'Payment captured successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function success()
    {
        return redirect()->route('dashboard')->with('message', 'Payment successful!');
    }

    public function cancel()
    {
        return redirect()->route('dashboard')->with('error', 'Payment cancelled.');
    }


}

