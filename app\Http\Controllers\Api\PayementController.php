<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PayPalService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class PayementController extends Controller
{
    private $paypalService;

    public function __construct(PayPalService $paypalService)
    {
        $this->paypalService = $paypalService;
    }

    public function createOrder(Request $request): JsonResponse
    {
        $request->validate([
            'montant' => 'required|numeric|min:0.01',
            'methode' => 'required|string|in:card,paypal,orange,mtn',
            'currency' => 'required|string|in:EUR,USD,XOF',
            'order_id' => 'required|string',
            'items' => 'required|array',
            'items.*.name' => 'required|string',
            'items.*.price' => 'required|numeric',
            'items.*.quantity' => 'required|integer|min:1',
        ]);

        // Vérifier si PayPal est configuré
        if (!$this->paypalService->isConfigured()) {
            return response()->json([
                'success' => false,
                'message' => 'PayPal n\'est pas configuré correctement'
            ], 500);
        }

        // Convertir FCFA en EUR si nécessaire
        $amount = $request->montant;
        $currency = $request->currency;
        $items = $request->items;

        if ($currency === 'XOF') {
            $amount = $amount * 0.0015; // Taux approximatif XOF -> EUR
            $currency = 'EUR';

            // Convertir aussi les prix des articles
            $items = array_map(function($item) {
                $item['price'] = $item['price'] * 0.0015;
                return $item;
            }, $items);
        }

        $orderData = [
            'order_id' => $request->order_id,
            'total' => $this->paypalService->formatAmount($amount),
            'items' => $items,
            'tax' => $request->tax ?? 0,
            'shipping' => $request->shipping ?? 0,
            'description' => $request->description ?? 'Commande Loʁelei Marketplace',
            'return_url' => route('payment.success'),
            'cancel_url' => route('payment.cancel'),
        ];

        $result = $this->paypalService->createPayment($orderData);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'payment_id' => $result['payment_id'],
                'approval_url' => $result['approval_url'],
                'message' => 'Commande créée avec succès'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['error']
            ], 500);
        }
    }

    public function executePayment(Request $request): JsonResponse
    {
        $request->validate([
            'payment_id' => 'required|string',
            'payer_id' => 'required|string',
        ]);

        $result = $this->paypalService->executePayment(
            $request->payment_id,
            $request->payer_id
        );

        if ($result['success']) {
            // Sauvegarder les détails de la transaction dans votre base de données
            // Exemple : Sauvegarder dans un modèle Payment
            // \App\Models\Payment::create([
            //     'user_id' => auth()->id(),
            //     'payment_id' => $request->payment_id,
            //     'transaction_id' => $result['transaction_id'],
            //     'amount' => $result['payment']->getTransactions()[0]->getAmount()->getTotal(),
            //     'currency' => $result['payment']->getTransactions()[0]->getAmount()->getCurrency(),
            //     'status' => $result['state'],
            //     'method' => 'paypal',
            // ]);

            return response()->json([
                'success' => true,
                'transaction_id' => $result['transaction_id'],
                'status' => $result['state'],
                'message' => 'Paiement exécuté avec succès'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['error']
            ], 500);
        }
    }

    public function getPaymentDetails(Request $request, string $paymentId): JsonResponse
    {
        $result = $this->paypalService->getPaymentDetails($paymentId);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'payment' => [
                    'id' => $paymentId,
                    'state' => $result['state'],
                    'total' => $result['total'],
                    'currency' => $result['currency']
                ]
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['error']
            ], 500);
        }
    }

    public function refundPayment(Request $request): JsonResponse
    {
        $request->validate([
            'sale_id' => 'required|string',
            'amount' => 'nullable|numeric|min:0.01',
        ]);

        $result = $this->paypalService->refundPayment(
            $request->sale_id,
            $request->amount
        );

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'refund_id' => $result['refund_id'],
                'state' => $result['state'],
                'message' => 'Remboursement effectué avec succès'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['error']
            ], 500);
        }
    }

    public function success()
    {
        return redirect()->route('dashboard')->with('message', 'Payment successful!');
    }

    public function cancel()
    {
        return redirect()->route('dashboard')->with('error', 'Payment cancelled.');
    }


}

