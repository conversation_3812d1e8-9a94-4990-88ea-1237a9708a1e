import { cn } from "@/lib/utils";
import { LoaderCircle } from "lucide-react";

interface LoaderProps {
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function Loader({ size = "md", className }: LoaderProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8",
  };

  return (
    <LoaderCircle
      className={cn(
        "animate-spin text-primary",
        sizeClasses[size],
        className
      )}
    />
  );
}

export function ButtonLoader({ size = "sm", className }: LoaderProps) {
  return <Loader size={size} className={cn("mr-2", className)} />;
}
