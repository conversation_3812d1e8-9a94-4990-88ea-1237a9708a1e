import React from 'react';
import { Head } from '@inertiajs/react';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import { useWishlist } from '@/contexts/WishlistContext';
import { Button } from '@/components/ui/button';
import { Link } from '@inertiajs/react';
import { ShoppingCart, Trash2, Heart } from 'lucide-react';
import { useCart } from '@/contexts/CartContext';
import { toast } from '@/hooks/use-toast';
import { ButtonLoader } from '@/components/ui/loader';
import { useState } from 'react';
import { useTranslation } from '@/hooks/use-translation';

/**
 * Page affichant la liste des souhaits de l'utilisateur
 */
export default function WishlistPage() {
  const { items, removeItem, clearWishlist } = useWishlist();
  const { addItem } = useCart();
  const [loadingItems, setLoadingItems] = useState<Record<string, boolean>>({});
  const { translate } = useTranslation();

  /**
   * Ajoute un produit au panier
   */
  const handleAddToCart = (productId: string) => {
    const product = items.find(item => item.id === productId);
    if (!product) return;

    // Marquer le produit comme en cours de chargement
    setLoadingItems(prev => ({ ...prev, [productId]: true }));

    // Simuler un délai pour montrer le chargement (peut être supprimé en production)
    setTimeout(() => {
      addItem(product);

      // Afficher une notification toast
      toast({
        title: "Produit ajouté au panier",
        description: `${product.name} a été ajouté à votre panier.`,
        variant: "success",
      });

      // Marquer le produit comme chargé
      setLoadingItems(prev => ({ ...prev, [productId]: false }));
    }, 600);
  };

  /**
   * Supprime un produit de la liste de souhaits
   */
  const handleRemoveFromWishlist = (productId: string) => {
    const product = items.find(item => item.id === productId);
    if (!product) return;

    // Marquer le produit comme en cours de chargement
    setLoadingItems(prev => ({ ...prev, [productId]: true }));

    // Simuler un délai pour montrer le chargement (peut être supprimé en production)
    setTimeout(() => {
      removeItem(productId);

      // Afficher une notification toast
      toast({
        title: "Produit retiré des favoris",
        description: `${product.name} a été retiré de votre liste de souhaits.`,
        variant: "default",
      });

      // Marquer le produit comme chargé
      setLoadingItems(prev => ({ ...prev, [productId]: false }));
    }, 300);
  };

  /**
   * Vide complètement la liste de souhaits
   */
  const handleClearWishlist = () => {
    if (items.length === 0) return;

    if (confirm('Êtes-vous sûr de vouloir vider votre liste de souhaits ?')) {
      clearWishlist();

      // Afficher une notification toast
      toast({
        title: "Liste de souhaits vidée",
        description: "Tous les produits ont été retirés de votre liste de souhaits.",
        variant: "default",
      });
    }
  };

  return (
    <EcommerceLayout>
      <Head title={translate('pages.wishlist.title')} />

      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <h1 className="text-2xl font-bold md:text-3xl">{translate('pages.wishlist.header')} </h1>

          {items.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearWishlist}
              className="text-muted-foreground"
            >
              <Trash2 className="mr-2 h-4 w-4" />
                {translate('pages.wishlist.clear_wishlist')}
            </Button>
          )}
        </div>

        {items.length === 0 ? (
          <div className="flex flex-col items-center justify-center rounded-lg border border-dashed p-12 text-center">
            <Heart className="mb-4 h-16 w-16 text-muted-foreground/50" />
            <h2 className="mb-2 text-xl font-medium">{translate('pages.wishlist.empty')} </h2>
            <p className="mb-6 max-w-md text-muted-foreground">
              {translate('pages.wishlist.empty_description')}
            </p>
            <Button asChild>
              <Link href={route('home')}>
                {translate('pages.wishlist.discover_products')}
              </Link>
            </Button>
          </div>
        ) : (
          <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
            {items.map((product) => (
              <div
                key={product.id}
                className="group relative overflow-hidden rounded-lg border bg-card shadow-sm transition-all hover:shadow-md"
              >
                <Link
                  href={route('product', { productSlug: product.slug })}
                  className="block"
                >
                  <div className="relative aspect-square overflow-hidden">
                    <img
                      src={product.imageUrl}
                      alt={product.name}
                      className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    {!product.inStock && (
                      <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                        <span className="rounded-md bg-red-500 px-3 py-1 text-sm font-medium text-white">
                          {translate('common.out_of_stock')}
                        </span>
                      </div>
                    )}
                  </div>
                </Link>

                <div className="p-3">
                  <Link
                    href={route('product', { productSlug: product.slug })}
                    className="block hover:underline"
                  >
                    <h3 className="mb-1 line-clamp-1 text-sm font-medium">{product.name}</h3>
                  </Link>

                  <p className="mb-2 line-clamp-1 text-xs text-muted-foreground">
                    {product.description}
                  </p>

                  <div className="mb-3 flex items-center gap-2">
                    <div className="text-base font-semibold">{product.formattedPrice()}</div>

                    {product.isOnSale() && (
                      <>
                        <div className="text-sm text-muted-foreground line-through">
                          {product.formattedOriginalPrice()}
                        </div>
                        <div className="rounded-md bg-red-100 px-1.5 py-0.5 text-xs font-medium text-red-800">
                          -{product.getDiscountPercentage()}%
                        </div>
                      </>
                    )}
                  </div>

                  <div className="flex gap-1">
                    <Button
                      onClick={() => handleAddToCart(product.id)}
                      className="flex-1 h-8 text-xs"
                      size="sm"
                      disabled={!product.inStock || loadingItems[product.id]}
                    >
                      {loadingItems[product.id] ? (
                        <>
                          <ButtonLoader />
                          {translate('common.adding_to_cart')}
                        </>
                      ) : (
                        <>
                          <ShoppingCart className="mr-2 h-4 w-4" />
                          {translate('common.add_to_cart')}
                        </>
                      )}
                    </Button>

                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => handleRemoveFromWishlist(product.id)}
                      disabled={loadingItems[product.id]}
                      className="h-8 w-8 text-red-500 hover:text-red-600"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </EcommerceLayout>
  );
}
