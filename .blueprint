created:
    - database/factories/UserFactory.php
    - database/factories/ClientFactory.php
    - database/factories/MarchandFactory.php
    - database/factories/ProduitFactory.php
    - database/factories/CategorieFactory.php
    - database/factories/CommandeFactory.php
    - database/factories/ArticleCommandeFactory.php
    - database/factories/PaiementFactory.php
    - database/factories/AdresseFactory.php
    - database/factories/BannerFactory.php
    - database/factories/CouponFactory.php
    - database/factories/AttributeFactory.php
    - database/migrations/2025_04_17_141214_create_users_table.php
    - database/migrations/2025_04_17_141215_create_clients_table.php
    - database/migrations/2025_04_17_141216_create_marchands_table.php
    - database/migrations/2025_04_17_141217_create_produits_table.php
    - database/migrations/2025_04_17_141218_create_categories_table.php
    - database/migrations/2025_04_17_141219_create_commandes_table.php
    - database/migrations/2025_04_17_141220_create_article_commandes_table.php
    - database/migrations/2025_04_17_141221_create_paiements_table.php
    - database/migrations/2025_04_17_141222_create_adresses_table.php
    - database/migrations/2025_04_17_141223_create_banners_table.php
    - database/migrations/2025_04_17_141224_create_coupons_table.php
    - database/migrations/2025_04_17_141225_create_attributes_table.php
    - app/Models/User.php
    - app/Models/Client.php
    - app/Models/Marchand.php
    - app/Models/Produit.php
    - app/Models/Categorie.php
    - app/Models/Commande.php
    - app/Models/ArticleCommande.php
    - app/Models/Paiement.php
    - app/Models/Adresse.php
    - app/Models/Banner.php
    - app/Models/Coupon.php
    - app/Models/Attribute.php
models:
    User: { id: 'uuid primary', email: 'string:255 unique', password: 'string:255', role: 'enum:Client,Marchand,Admin', created_at: timestamp, last_login_at: 'nullable timestamp', is_active: 'boolean default:true', email_verification_token: 'nullable string:100 unique', password_reset_token: 'nullable string:100 unique', password_reset_expires_at: 'nullable timestamp', relationships: { hasMany: 'Client, Marchand' } }
    Client: { id: 'uuid primary', user_id: 'uuid foreign', prenom: 'string:100', nom: 'string:100', adresse_id: 'uuid foreign:adresses nullable', telephone: 'string:20 nullable', dateDeNaissance: 'date nullable', relationships: { belongsTo: 'User, Adresse', hasMany: Commande } }
    Marchand: { id: 'uuid primary', user_id: 'uuid foreign', nomEntreprise: 'string:255', adresse_id: 'uuid foreign:adresses nullable', idFiscal: 'string:50 nullable', banqueNom: 'string:100 nullable', banqueNumeroCompte: 'string:50 nullable', relationships: { belongsTo: 'User, Adresse', hasMany: 'Produit, Commande, Paiement' } }
    Produit: { id: 'uuid primary', marchand_id: 'uuid foreign', nom: 'string:255', description: text, prix: 'decimal:10,2', stock: integer, images: 'json nullable', categorie_id: 'uuid foreign', creeLe: timestamp, misAJourLe: 'timestamp nullable', poids: 'decimal:8,2 nullable', dimensions: 'json nullable', discount_price: 'decimal:10,2 nullable', discount_start_date: 'timestamp nullable', discount_end_date: 'timestamp nullable', relationships: { belongsTo: 'Marchand, Categorie', hasMany: ArticleCommande } }
    Categorie: { id: 'uuid primary', nom: 'string:255', categorie_parent_id: 'uuid foreign:categories nullable', description: 'text nullable', image_url: 'string:255 nullable', relationships: { belongsTo: Categorie, hasMany: 'Categorie, Produit' } }
    Commande: { id: 'uuid primary', client_id: 'uuid foreign', marchand_id: 'uuid foreign', montantTotal: 'decimal:10,2', statut: 'enum:EnAttente,Expédié,Livré,Annulé,EnCoursDeTraitement,Remboursé', adresse_livraison_id: 'uuid foreign:adresses', creeLe: timestamp, dateExpeditionPrevue: 'date nullable', dateLivraisonPrevue: 'date nullable', codeSuivi: 'string:100 nullable', relationships: { belongsTo: 'Client, Marchand, Adresse', hasMany: ArticleCommande } }
    ArticleCommande: { id: 'uuid primary', commande_id: 'uuid foreign', produit_id: 'uuid foreign', quantite: integer, prixUnitaire: 'decimal:10,2', relationships: { belongsTo: 'Commande, Produit' } }
    Paiement: { id: 'uuid primary', marchand_id: 'uuid foreign', commande_id: 'uuid foreign:commandes nullable', montant: 'decimal:10,2', statut: 'enum:EnAttente,Complété,Échoué,Remboursé', creeLe: timestamp, transaction_id: 'string:255 nullable', methode: 'string:100 nullable', relationships: { belongsTo: 'Marchand, Commande' } }
    Adresse: { id: 'uuid primary', rue: 'string:255', ville: 'string:100', etat: 'string:100', pays: 'string:100', codePostal: 'string:20', type: 'enum:Livraison,Facturation,Entreprise default:Livraison', relationships: { hasMany: 'Client, Marchand, Commande' } }
    Banner: { id: 'uuid primary', image_url: 'string:255', target_url: 'string:255 nullable', position: 'string:100', start_date: 'timestamp nullable', end_date: 'timestamp nullable', is_active: 'boolean default:true', priorite: 'integer default:0' }
    Coupon: { id: 'uuid primary', code: 'string:50 unique', type: 'enum:Pourcentage,MontantFixe default:Pourcentage', valeur: 'decimal:10,2', date_debut: 'timestamp nullable', date_fin: 'timestamp nullable', utilisation_max: 'integer nullable', utilisation_compteur: 'integer default:0', est_actif: 'boolean default:true' }
    Attribute: { id: 'uuid primary', nom: 'string:255', relationships: { hasMany: AttributeValue } }
