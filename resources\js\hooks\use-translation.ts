import { useState, useEffect } from 'react';

// Importation des fichiers de traduction
import frCommon from '/lang/ecommerce/fr.json';
import enCommon from '/lang/ecommerce/en.json';
import frPages from '/lang/ecommerce/pages/fr.json';
import enPages from '/lang/ecommerce/pages/en.json';

// Type pour les langues supportées
type Locale = 'fr' | 'en';

/**
 * Hook pour gérer les traductions et le changement de langue
 */
export function useTranslation() {
  // Récupérer la langue actuelle depuis localStorage ou utiliser le français par défaut
  const [currentLocale, setCurrentLocale] = useState<Locale>(() => {
    if (typeof window !== 'undefined') {
      const storedLocale = localStorage.getItem('locale');
      return (storedLocale === 'fr' || storedLocale === 'en') ? storedLocale : 'fr';
    }
    return 'fr';
  });

  // Mettre à jour localStorage lorsque la langue change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('locale', currentLocale);
    }
  }, [currentLocale]);

  /**
   * Change la langue de l'application
   *
   * @param locale - Code de la langue
   */
  const setLocale = (locale: Locale) => {
    setCurrentLocale(locale);
  };

  /**
   * Traduit une clé en fonction de la langue actuelle
   *
   * @param key - Clé de traduction
   * @param params - Paramètres à insérer dans la traduction
   * @returns La traduction correspondante
   */
  /**
   * Récupère une traduction à partir d'une clé imbriquée
   * Exemple: t('product.details') va chercher translations.product.details
   *
   * Si la traduction n'existe pas dans la langue actuelle, essaie avec le français puis l'anglais.
   * Si aucune traduction n'est trouvée, retourne la clé d'origine et affiche un avertissement.
   *
   * @param key - Clé de traduction (format: 'section.sous_section.clé')
   * @param params - Paramètres à insérer dans la traduction
   * @returns La traduction correspondante ou la clé si aucune traduction n'est trouvée
   */
  const translate = (key: string, params: Record<string, string | number> = {}): string => {
    // Si la clé est vide, retourner une chaîne vide
    if (!key) {
      console.warn('Translation key is empty');
      return '';
    }

    // Fusionner les traductions des fichiers JSON importés
    const translations: Record<Locale, Record<string, unknown>> = {
      fr: { ...frCommon, ...frPages },
      en: { ...enCommon, ...enPages }
    };
    // Diviser la clé en parties (ex: 'product.details' -> ['product', 'details'])
    const keyParts = key.split('.');

    // Récupérer les traductions pour la langue actuelle
    const localeTranslations = translations[currentLocale];

    // Fonction pour récupérer une traduction à partir d'un objet et d'une liste de parties de clé
    const getTranslation = (obj: Record<string, unknown>, parts: string[]): string | null => {
      let current = obj;

      for (const part of parts) {
        if (current && typeof current === 'object' && part in current) {
          current = current[part] as Record<string, unknown>;
        } else {
          return null;
        }
      }

      return typeof current === 'string' ? current : null;
    };

    // Essayer de trouver la traduction dans la langue actuelle
    let translation = getTranslation(localeTranslations, keyParts);

    // Si la traduction n'existe pas dans la langue actuelle, essayer avec le français comme fallback
    if (translation === null && currentLocale !== 'fr') {
      translation = getTranslation(translations['fr'], keyParts);

      // Si toujours pas de traduction, essayer avec l'anglais comme second fallback
      if (translation === null) {
        translation = getTranslation(translations['en'], keyParts);
      }
    }

    // Si aucune traduction n'est trouvée, utiliser la clé comme fallback et logger un avertissement
    if (translation === null) {
      console.warn(`Translation missing for key: ${key}`);
      return key;
    }

    // Remplacer les paramètres dans la traduction
    let result = translation;

    // Parcourir tous les paramètres et les remplacer dans la chaîne
    for (const [paramKey, paramValue] of Object.entries(params)) {
      const regex = new RegExp(`{${paramKey}}`, 'g');
      result = result.replace(regex, String(paramValue));
    }

    return result;
  };

  /**
   * Vérifie si une clé de traduction existe
   *
   * @param key - Clé de traduction à vérifier
   * @returns true si la clé existe, false sinon
   */
  const hasTranslation = (key: string): boolean => {
    if (!key) return false;

    const translations: Record<Locale, Record<string, unknown>> = {
      fr: { ...frCommon, ...frPages },
      en: { ...enCommon, ...enPages }
    };

    const keyParts = key.split('.');
    const localeTranslations = translations[currentLocale];

    // Fonction pour vérifier si une traduction existe
    const checkTranslation = (obj: Record<string, unknown>, parts: string[]): boolean => {
      let current = obj;

      for (const part of parts) {
        if (current && typeof current === 'object' && part in current) {
          current = current[part] as Record<string, unknown>;
        } else {
          return false;
        }
      }

      return typeof current === 'string';
    };

    // Vérifier dans la langue actuelle
    if (checkTranslation(localeTranslations, keyParts)) {
      return true;
    }

    // Vérifier dans le français comme fallback
    if (currentLocale !== 'fr' && checkTranslation(translations['fr'], keyParts)) {
      return true;
    }

    // Vérifier dans l'anglais comme second fallback
    return checkTranslation(translations['en'], keyParts);
  };

  /**
   * Traduit une clé en fonction de la langue actuelle avec une valeur par défaut
   *
   * @param key - Clé de traduction
   * @param defaultValue - Valeur par défaut à utiliser si la traduction n'existe pas
   * @param params - Paramètres à insérer dans la traduction
   * @returns La traduction correspondante ou la valeur par défaut
   */
  const translateWithDefault = (
    key: string,
    defaultValue: string,
    params: Record<string, string | number> = {}
  ): string => {
    if (hasTranslation(key)) {
      return translate(key, params);
    }

    // Remplacer les paramètres dans la valeur par défaut
    let result = defaultValue;
    for (const [paramKey, paramValue] of Object.entries(params)) {
      const regex = new RegExp(`{${paramKey}}`, 'g');
      result = result.replace(regex, String(paramValue));
    }

    return result;
  };

  return {
    currentLocale,
    setLocale,
    t: translate, // Alias pour la compatibilité avec le code existant
    translate,    // Nouveau nom plus explicite
    traducteur: translate, // Alias en français
    hasTranslation, // Fonction pour vérifier si une clé existe
    translateWithDefault, // Fonction pour traduire avec une valeur par défaut
    tDefault: translateWithDefault, // Alias plus court
  };
}
