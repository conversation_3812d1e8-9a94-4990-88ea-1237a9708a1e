<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Translatable\HasTranslations;

class Produit extends Model
{
    use HasFactory, HasTranslations;

    /**
     * Les attributs qui sont traduisibles.
     *
     * @var array
     */
    public $translatable = ['nom', 'description'];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'marchand_id',
        'nom',
        'slug',
        'product_code',
        'marque',
        'description',
        'prix',
        'currency',
        'stock',
        'images',
        'categorie_id',
        'creeLe',
        'misAJourLe',
        'poids',
        'attributs', // Renommé de 'dimensions' à 'attributs'
        'discount_price',
        'discount_start_date',
        'discount_end_date',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['image_urls', 'main_image_urls', 'additional_image_urls', 'thumbnail_urls', 'average_rating', 'reviews_count'];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'prix' => 'decimal:2',
            'images' => 'array',
            'creeLe' => 'timestamp',
            'misAJourLe' => 'timestamp',
            'poids' => 'decimal:2',
            'attributs' => 'array', // Renommé de 'dimensions' à 'attributs'
            'discount_price' => 'decimal:2',
            'discount_start_date' => 'timestamp',
            'discount_end_date' => 'timestamp',
        ];
    }

    /**
     * Get the full URLs for the product images.
     *
     * @return array
     */
    public function getImageUrlsAttribute(): array
    {
        if (empty($this->images)) {
            return [];
        }

        // Si images est une chaîne JSON, la décoder d'abord
        $images = $this->images;
        if (is_string($images)) {
            $images = json_decode($images, true) ?? [];
        }

        return array_map(function ($image) {
            // Vérifier si l'image est déjà un chemin complet
            if (strpos($image, '/') !== false) {
                return url('/images/' . $image);
            }

            // Déterminer le dossier basé sur l'ID du produit
            $folderPrefix = $this->id < 1000 ? '0' : substr((string)$this->id, 0, -3);
            return url("/images/products/{$folderPrefix}/{$image}");
        }, $images);
    }

    /**
     * Get the full URLs for the main product images (maximum 2).
     *
     * @return array
     */
    public function getMainImageUrlsAttribute(): array
    {
        $allImages = $this->getImageUrlsAttribute();

        // Retourner les deux premières images (ou moins s'il n'y en a pas assez)
        return array_slice($allImages, 0, 2);
    }

    /**
     * Get the full URLs for the additional product images (all except the first 2).
     *
     * @return array
     */
    public function getAdditionalImageUrlsAttribute(): array
    {
        $allImages = $this->getImageUrlsAttribute();

        // Retourner toutes les images sauf les deux premières
        return array_slice($allImages, 2);
    }

    /**
     * Get the thumbnail URLs for all product images.
     *
     * @return array
     */
    public function getThumbnailUrlsAttribute(): array
    {
        if (empty($this->images)) {
            return [
                'small' => [],
                'medium' => [],
                'large' => []
            ];
        }

        // Si images est une chaîne JSON, la décoder d'abord
        $images = $this->images;
        if (is_string($images)) {
            $images = json_decode($images, true) ?? [];
        }

        $thumbnails = [
            'small' => [],
            'medium' => [],
            'large' => []
        ];

        foreach ($images as $image) {
            // Utiliser le helper pour obtenir les URLs des miniatures
            $thumbnails['small'][] = \App\Helpers\ThumbnailHelper::getThumbnailUrl($image, 'small');
            $thumbnails['medium'][] = \App\Helpers\ThumbnailHelper::getThumbnailUrl($image, 'medium');
            $thumbnails['large'][] = \App\Helpers\ThumbnailHelper::getThumbnailUrl($image, 'large');
        }

        return $thumbnails;
    }

    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    public function categorie(): BelongsTo
    {
        return $this->belongsTo(Categorie::class);
    }

    public function articleCommandes(): HasMany
    {
        return $this->hasMany(ArticleCommande::class);
    }

    /**
     * Get the reviews for the product.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the average rating for the product.
     */
    public function getAverageRatingAttribute(): float
    {
        return $this->reviews()->where('is_approved', true)->avg('rating') ?? 0;
    }

    /**
     * Get the number of reviews for the product.
     */
    public function getReviewsCountAttribute(): int
    {
        return $this->reviews()->where('is_approved', true)->count();
    }

    /**
     * Get the variants for the product.
     */
    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class, 'produit_id');
    }

    /**
     * Get the sizes available for this product.
     */
    public function sizes()
    {
        return $this->belongsToMany(Size::class, 'produit_size')
            ->withTimestamps();
    }



    /**
     * Generate a slug from the name.
     */
    public function setNomAttribute($value)
    {
        $this->attributes['nom'] = $value;

        // Générer un slug si le modèle est nouveau ou si le slug est vide
        if (!$this->exists || empty($this->slug)) {
            $this->attributes['slug'] = $this->generateUniqueSlug($value);
        }
    }

    /**
     * Generate a unique slug.
     *
     * @param string $name
     * @return string
     */
    protected function generateUniqueSlug($name)
    {
        if (empty($name)) {
            $name = 'produit-' . uniqid();
        }

        $slug = \Illuminate\Support\Str::slug($name);
        $originalSlug = $slug;
        $count = 1;

        // Vérifier si le slug existe déjà
        while (static::where('slug', $slug)->exists()) {
            $slug = "{$originalSlug}-{$count}";
            $count++;
        }

        return $slug;
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        // Utiliser l'ID pour les routes Filament, le slug pour les routes frontend
        if (request()->is('admin/*') || request()->is('marchand/*')) {
            return 'id';
        }

        return 'slug';
    }
    /**
     * Obtenir les zones de livraison associées à ce produit.
     */
    public function zonesLivraison(): BelongsToMany
    {
        return $this->belongsToMany(ZoneLivraison::class, 'produit_zones_livraison', 'produit_id', 'marchand_zone_livraison_id')
            ->withPivot(['frais_livraison_specifique', 'actif'])
            ->withTimestamps();
    }

    /**
     * Obtenir les associations produit-zone de livraison.
     */
    public function produitZonesLivraison(): HasMany
    {
        return $this->hasMany(ProduitZoneLivraison::class, 'produit_id');
    }

    /**
     * Vérifier si le produit peut être livré dans une zone spécifique.
     *
     * @param int $zoneId
     * @return bool
     */
    public function peutEtreLivreDansZone(int $zoneId): bool
    {
        // Vérifier si le produit a des zones de livraison spécifiques actives
        $produitZones = $this->produitZonesLivraison()
            ->whereHas('marchandZoneLivraison', function ($query) use ($zoneId) {
                $query->where('zone_livraison_id', $zoneId)
                    ->where('actif', true);
            })
            ->where('actif', true)
            ->exists();

        if ($produitZones) {
            return true;
        }

        // Si le produit n'a pas de zones spécifiques, vérifier les zones du marchand
        return MarchandZoneLivraison::where('marchand_id', $this->marchand_id)
            ->where('zone_livraison_id', $zoneId)
            ->where('actif', true)
            ->exists();
    }
}
