<?php

namespace App\Filament\Marchand\Resources\ProduitResource\RelationManagers;

use App\Models\MarchandZoneLivraison;
use App\Models\ProduitZoneLivraison;
use App\Models\ZoneLivraison;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class ZonesLivraisonRelationManager extends RelationManager
{
    protected static string $relationship = 'produitZonesLivraison';

    public function form(Form $form): Form
    {
        $marchandId = Auth::id();

        return $form
            ->schema([
                Forms\Components\Hidden::make('produit_id')
                    ->default(fn ($livewire) => $livewire->getOwnerRecord()->id),

                Forms\Components\Select::make('marchand_zone_livraison_id')
                    ->label('Zone de livraison')
                    ->options(function () use ($marchandId) {
                        // Récupérer les zones de livraison du marchand
                        return MarchandZoneLivraison::where('marchand_id', $marchandId)
                            ->where('actif', true)
                            ->with('zoneLivraison')
                            ->get()
                            ->mapWithKeys(function ($item) {
                                $prefix = match ($item->zoneLivraison->type) {
                                    'Pays' => '🌍 ',
                                    'Region' => '🏞️ ',
                                    'Ville' => '🏙️ ',
                                    'Quartier' => '🏘️ ',
                                    default => '',
                                };

                                return [
                                    $item->id => $prefix . $item->zoneLivraison->nom . ' (' .
                                    number_format($item->frais_livraison, 0, ',', ' ') . ' FCFA, ' .
                                    $item->delai_livraison_min . '-' . $item->delai_livraison_max . ' jours)'
                                ];
                            });
                    })
                    ->searchable()
                    ->preload()
                    ->required()
                    ->helperText('Sélectionnez une zone de livraison que vous avez configurée'),

                Forms\Components\TextInput::make('frais_livraison_specifique')
                    ->label('Frais de livraison spécifiques (FCFA)')
                    ->helperText('Laissez vide pour utiliser les frais standard de la zone')
                    ->numeric()
                    ->minValue(0)
                    ->step(100),

                Forms\Components\Toggle::make('actif')
                    ->label('Actif')
                    ->helperText('Activez ou désactivez cette zone de livraison pour ce produit')
                    ->default(true)
                    ->required(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->modifyQueryUsing(fn (Builder $query) => $query->with(['marchandZoneLivraison', 'marchandZoneLivraison.zoneLivraison']))
            ->columns([
                Tables\Columns\TextColumn::make('marchandZoneLivraison.zoneLivraison.nom')
                    ->label('Zone de livraison')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('marchandZoneLivraison.zoneLivraison.type')
                    ->label('Type de zone')
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'Pays' => 'success',
                        'Region' => 'info',
                        'Ville' => 'warning',
                        'Quartier' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('frais_livraison')
                    ->label('Frais de livraison')
                    ->money('XAF')
                    ->sortable(),

                Tables\Columns\TextColumn::make('delai_livraison')
                    ->label('Délai de livraison'),

                Tables\Columns\IconColumn::make('actif')
                    ->label('Actif')
                    ->boolean()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('zone_type')
                    ->label('Type de zone')
                    ->options([
                        'Pays' => 'Pays',
                        'Region' => 'Région',
                        'Ville' => 'Ville',
                        'Quartier' => 'Quartier',
                    ])
                    ->query(function (Builder $query, array $data) {
                        return $query->when(
                            $data['value'],
                            fn (Builder $query, $value) => $query->whereHas('marchandZoneLivraison.zoneLivraison', fn ($q) => $q->where('type', $value))
                        );
                    }),

                Tables\Filters\TernaryFilter::make('actif')
                    ->label('Actif')
                    ->boolean(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Ajouter une zone de livraison')
                    ->modalHeading('Ajouter une zone de livraison pour ce produit')
                    ->modalDescription('Sélectionnez une zone de livraison et définissez éventuellement des frais spécifiques pour ce produit.'),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Modifier')
                    ->modalHeading('Modifier la zone de livraison')
                    ->modalDescription('Modifiez les paramètres de livraison pour cette zone.'),

                Tables\Actions\DeleteAction::make()
                    ->label('Supprimer')
                    ->modalHeading('Supprimer la zone de livraison')
                    ->modalDescription('Êtes-vous sûr de vouloir supprimer cette zone de livraison pour ce produit ? Cette action est irréversible.'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->label('Supprimer la sélection')
                        ->modalHeading('Supprimer les zones de livraison')
                        ->modalDescription('Êtes-vous sûr de vouloir supprimer ces zones de livraison pour ce produit ? Cette action est irréversible.'),
                ]),
            ])
            ->defaultSort('marchandZoneLivraison.zoneLivraison.nom');
    }
}
