import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { Product } from '@/models/Product';
import CardProduit from './CardProduit';
import { ProductService } from '@/services/ProductService';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { Loader2, X } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import { PaginationWithLimit } from '@/components/ui/pagination-with-limit';

interface ProductListInfiniteProps {
    categoryId?: string;
    initialProducts?: Product[];
    itemsPerPage?: number;
    className?: string;
    emptyMessage?: string;
    filters?: Record<string, string | number | boolean>;
    showPagination?: boolean;
    onFilterChange?: (filters: Record<string, string | number | boolean>) => void;
}

/**
 * Composant pour afficher une liste de produits avec chargement progressif et pagination ajustable
 */
export default function ProductListInfinite({
    categoryId,
    initialProducts = [],
    itemsPerPage = 10,
    className = '',
    emptyMessage = 'Aucun produit trouvé',
    filters = {},
    showPagination = false,
    onFilterChange
}: ProductListInfiniteProps) {
    const [products, setProducts] = useState<Product[]>(initialProducts);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [totalPages, setTotalPages] = useState<number>(1);
    const [totalItems, setTotalItems] = useState<number>(0);
    const [allowedLimits, setAllowedLimits] = useState<number[]>([10, 20, 50, 100]);
    const [perPage, setPerPage] = useState<number>(itemsPerPage);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const { tDefault } = useTranslation();
    const productService = useMemo(() => new ProductService(), []);

    // Utilisation du hook de défilement infini
    // Nous utilisons un type plus générique pour éviter les problèmes de compatibilité
    const infiniteScrollRef = useRef<any>(null);

    // Les filtres sont maintenant correctement transmis à l'API
    // Fonction de chargement des produits pour le mode pagination
    const loadProductsPaginated = useCallback(async (page: number, limit: number = perPage) => {
        setIsLoading(true);
        try {
            let result;

            if (categoryId) {
                result = await productService.getProductsByCategoryPaginated(categoryId, page, limit, filters);
            } else {
                result = await productService.getProductsPaginated(page, limit, filters);
            }
            if (!result || result.products.length === 0) return false;
            setProducts(result.products);
            setCurrentPage(result.currentPage);
            setTotalPages(result.totalPages);
            setTotalItems(result.totalItems);
            setAllowedLimits(result.allowedLimits);

        } catch (error) {
            console.error('Erreur lors du chargement des produits:', error);
        } finally {
            setIsLoading(false);
        }
    }, [categoryId, filters, perPage, productService]);

    // Fonction de chargement des produits pour le mode infini
    const loadProductsInfinite = useCallback(async (page: number): Promise<boolean> => {
        try {
            let result;

            if (categoryId) {
                result = await productService.getProductsByCategoryPaginated(categoryId, page, perPage, filters);
            } else {
                result = await productService.getProductsPaginated(page, perPage, filters);
            }
            if (!result || result.products.length === 0) return false;

            // Mettre à jour la liste des produits
            if (page === 1) {
                setProducts(result.products);
                // Mettre à jour la référence de longueur pour le hook useInfiniteScroll
                if (infiniteScrollRef.current && infiniteScrollRef.current.previousProductsLength) {
                    infiniteScrollRef.current.previousProductsLength.current = result.products.length;
                }
            } else {
                setProducts(prevProducts => {
                    // Vérifier si de nouveaux produits ont été ajoutés
                    const existingProductIds = prevProducts.map(p => p.id);
                    const uniqueNewProducts = result.products.filter(p => !existingProductIds.includes(p.id));

                    if (uniqueNewProducts.length === 0) {
                        console.log('Aucun nouveau produit unique trouvé, arrêt du chargement');
                        return prevProducts; // Aucun nouveau produit unique, ne pas mettre à jour
                    }

                    const updatedProducts = [...prevProducts, ...uniqueNewProducts];

                    // Mettre à jour la référence de longueur pour le hook useInfiniteScroll
                    if (infiniteScrollRef.current && infiniteScrollRef.current.previousProductsLength) {
                        infiniteScrollRef.current.previousProductsLength.current = updatedProducts.length;
                    }

                    return updatedProducts;
                });
            }

            return result.hasMore;
        } catch (error) {
            console.error('Erreur lors du chargement des produits:', error);
            return false;
        }
    }, [categoryId, filters, perPage, productService]);

    // Utilisation du hook de défilement infini
    const { ref, loading, hasMore } = useInfiniteScroll(loadProductsInfinite, {
        enabled: initialProducts.length === 0 && !showPagination && Object.keys(filters).length === 0, // Ne pas charger automatiquement si des produits initiaux sont fournis, si on est en mode pagination, ou si des filtres sont appliqués
    });

    // Stocker la référence pour pouvoir y accéder dans loadProductsInfinite
    useEffect(() => {
        infiniteScrollRef.current = ref;
    }, [ref]);

    // Effet exécuté au montage du composant et lorsque initialProducts change
    useEffect(() => {
        // Utiliser les produits initiaux s'ils sont fournis
        if (initialProducts.length > 0) {
            setProducts(initialProducts);
            setCurrentPage(1);
        } else if (showPagination) {
            // En mode pagination, charger la première page si aucun produit initial n'est fourni
            loadProductsPaginated(1, perPage);
        }
    }, [initialProducts, showPagination, perPage, loadProductsPaginated]);

    // Gérer le changement de page
    const handlePageChange = (page: number) => {
        loadProductsPaginated(page, perPage);
    };

    // Gérer le changement du nombre d'éléments par page
    const handleItemsPerPageChange = (limit: number) => {
        setPerPage(limit);
        loadProductsPaginated(1, limit); // Revenir à la première page lors du changement de limite
    };

    // Fonction pour gérer le clic sur un filtre de notation
    const handleRatingFilterClick = (rating: number) => {
        // Si un callback onFilterChange est fourni, l'appeler avec le nouveau filtre
        if (onFilterChange) {
            const newFilters = { ...filters, rating };
            onFilterChange(newFilters);
        }
    };
    // Afficher un message si aucun produit n'est trouvé
    if (products.length === 0 && !isLoading) {
        return <div className="py-8 text-center text-muted-foreground">{emptyMessage}</div>;
    }
    return (
        <div className={className}>
            {/* Filtres de notation (exemple d'utilisation) */}
            {onFilterChange && (
                <div className="mb-6 flex flex-wrap gap-2">
                    {[5, 4, 3, 2, 1].map((rating) => (
                        <button
                            key={rating}
                            className={`flex items-center rounded-md border px-3 py-1 text-sm ${filters.rating === rating ? 'border-primary bg-primary/10' : 'border-muted'
                                }`}
                            onClick={() => handleRatingFilterClick(rating)}
                        >
                            {Array.from({ length: rating }).map((_, i) => (
                                <span key={i} className="text-yellow-500">★</span>
                            ))}
                            {Array.from({ length: 5 - rating }).map((_, i) => (
                                <span key={i} className="text-muted">★</span>
                            ))}
                        </button>
                    ))}
                    {filters.rating && (
                        <button
                            className="flex items-center rounded-md border border-muted px-3 py-1 text-sm"
                            onClick={() => {
                                if (onFilterChange) {
                                    // Supprimer le filtre de notation et conserver les autres filtres
                                    const newFilters = { ...filters };
                                    delete newFilters.rating;
                                    onFilterChange(newFilters);
                                }
                            }}
                        >
                            <X className="mr-1 h-3 w-3" />
                            {tDefault('filters.reset', 'Reset')}
                        </button>
                    )}
                </div>
            )}

            <div className="grid grid-cols-2 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                {products.map((product) => (
                    <CardProduit key={product.id} product={product} />
                ))}
            </div>

            {/* Mode défilement infini */}
            {!showPagination && (loading || hasMore) && (
                <div ref={ref} className="mt-8 flex justify-center py-4">
                    {loading ? (
                        <div className="flex items-center gap-2">
                            <Loader2 className="h-5 w-5 animate-spin" />
                            <span>{tDefault('common.loading', 'Loading...')}</span>
                        </div>
                    ) : (
                        <div className="h-8" />
                    )}
                </div>
            )}

            {/* Mode pagination */}
            {showPagination && (
                <div className="mt-8">
                    {isLoading ? (
                        <div className="flex justify-center py-4">
                            <div className="flex items-center gap-2">
                                <Loader2 className="h-5 w-5 animate-spin" />
                                <span>{tDefault('common.loading', 'Loading...')}</span>
                            </div>
                        </div>
                    ) : (
                        <PaginationWithLimit
                            currentPage={currentPage}
                            totalPages={totalPages}
                            onPageChange={handlePageChange}
                            itemsPerPage={perPage}
                            onItemsPerPageChange={handleItemsPerPageChange}
                            allowedLimits={allowedLimits}
                            totalItems={totalItems}
                            className="mt-4"
                        />
                    )}
                </div>
            )}
        </div>
    );
}

