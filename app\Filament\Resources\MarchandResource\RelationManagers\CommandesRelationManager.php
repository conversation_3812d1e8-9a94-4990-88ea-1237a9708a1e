<?php

namespace App\Filament\Resources\MarchandResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CommandesRelationManager extends RelationManager
{
    protected static string $relationship = 'commandes';

    protected static ?string $recordTitleAttribute = 'id';

    protected static ?string $title = 'Commandes';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('id')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('client.user.email')
                    ->label('Client')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('montantTotal')
                    ->label('Montant total')
                    ->money('EUR')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('statut')
                    ->label('Statut')
                    ->colors([
                        'primary' => 'EnAttente',
                        'warning' => 'EnCoursDeTraitement',
                        'success' => 'Expédié',
                        'success' => 'Livré',
                        'danger' => 'Annulé',
                        'danger' => 'Remboursé',
                    ]),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créée le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('statut')
                    ->label('Statut')
                    ->options([
                        'EnAttente' => 'En attente',
                        'EnCoursDeTraitement' => 'En cours de traitement',
                        'Expédié' => 'Expédié',
                        'Livré' => 'Livré',
                        'Annulé' => 'Annulé',
                        'Remboursé' => 'Remboursé',
                    ]),
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Créée depuis'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Créée jusqu\'à'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->headerActions([
                // Pas d'action de création ici car les commandes sont créées par les clients
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Pas d'action de suppression en masse pour les commandes
                ]),
            ]);
    }
}
