import React, { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Product } from '@/models/Product';
import { ProductVariant, ProductAttribute } from '@/models/ProductVariant';
import { useDelivery } from '@/contexts/DeliveryContext';
import { DeliveryInfo as DeliveryInfoType } from '@/models/CartItem';
import { Loader2, ShoppingCart } from 'lucide-react';
import { useTranslation } from '@/hooks/use-translation';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import ZoneLivraisonSelector from './ZoneLivraisonSelector';
import DeliveryInfo from './DeliveryInfo';
import { toast } from '@/hooks/use-toast';
import zoneLivraisonService, { AvailableZoneData } from '@/services/zoneLivraisonService';

interface ProductCardModalProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
  onAddToCart: (product: Product, quantity: number, deliveryInfo?: DeliveryInfoType) => void;
}

export default function ProductCardModal({ product, isOpen, onClose, onAddToCart }: ProductCardModalProps) {
  const { selectedZone, getDeliveryInfo } = useDelivery();
  const { translate } = useTranslation();
  const [isLoading, setIsLoading] = useState(true);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [availableZones, setAvailableZones] = useState<AvailableZoneData[]>([]);
  
  // États pour les attributs sélectionnés
  const [selectedAttributes, setSelectedAttributes] = useState<{
    color: string | null;
    size: string | null;
    material: string | null;
  }>({
    color: null,
    size: null,
    material: null
  });
  
  // État pour la variante sélectionnée
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  
  // Charger les données du produit lorsque le modal s'ouvre
  useEffect(() => {
    if (isOpen && product) {
      setIsLoading(true);
      
      // Réinitialiser les états
      setSelectedAttributes({
        color: null,
        size: null,
        material: null
      });
      setSelectedVariant(null);
      setQuantity(1);
      
      // Charger les zones disponibles pour ce produit
      if (product.id) {
        zoneLivraisonService.getAvailableZonesForProduct(parseInt(product.id))
          .then(zones => {
            setAvailableZones(zones);
          })
          .catch(error => {
            console.error('Erreur lors du chargement des zones disponibles:', error);
          })
          .finally(() => {
            setIsLoading(false);
          });
      } else {
        setIsLoading(false);
      }
    }
  }, [isOpen, product]);
  
  // Mettre à jour la variante sélectionnée lorsque les attributs changent
  useEffect(() => {
    if (product && product.variants.length > 0) {
      // Trouver la variante qui correspond aux attributs sélectionnés
      const matchingVariant = product.variants.find(variant => {
        // Vérifier si tous les attributs sélectionnés correspondent à cette variante
        let colorMatch = !selectedAttributes.color;
        let sizeMatch = !selectedAttributes.size;
        let materialMatch = !selectedAttributes.material;
        
        for (const attr of variant.attributes) {
          if (attr.type === 'couleur' && 'nom' in attr && selectedAttributes.color) {
            colorMatch = attr.nom === selectedAttributes.color;
          } else if (attr.type === 'taille' && 'valeur' in attr && selectedAttributes.size) {
            sizeMatch = attr.valeur === selectedAttributes.size;
          } else if (attr.type === 'matiere' && 'valeur' in attr && selectedAttributes.material) {
            materialMatch = attr.valeur === selectedAttributes.material;
          }
        }
        
        return colorMatch && sizeMatch && materialMatch;
      });
      
      setSelectedVariant(matchingVariant || null);
    }
  }, [product, selectedAttributes]);
  
  // Extraire les options disponibles pour chaque type d'attribut
  const getAttributeOptions = (type: 'couleur' | 'taille' | 'matiere') => {
    if (!product) return [];
    
    const options = new Set<string>();
    
    // Ajouter les options des variantes
    product.variants.forEach(variant => {
      variant.attributes.forEach(attr => {
        if (attr.type === type) {
          if ('nom' in attr && type === 'couleur') {
            options.add(attr.nom);
          } else if ('valeur' in attr && (type === 'taille' || type === 'matiere')) {
            options.add(attr.valeur);
          }
        }
      });
    });
    
    // Ajouter les options du produit principal
    product.attributes.forEach(attr => {
      if (attr.type === type) {
        if ('nom' in attr && type === 'couleur') {
          options.add(attr.nom);
        } else if ('valeur' in attr && (type === 'taille' || type === 'matiere')) {
          options.add(attr.valeur);
        }
      }
    });
    
    return Array.from(options);
  };
  
  const colorOptions = getAttributeOptions('couleur');
  const sizeOptions = getAttributeOptions('taille');
  const materialOptions = getAttributeOptions('matiere');
  
  // Vérifier si le produit a des variantes
  const hasVariants = colorOptions.length > 0 || sizeOptions.length > 0 || materialOptions.length > 0;
  
  // Gérer l'ajout au panier
  const handleAddToCart = async () => {
    if (!product) return;
    
    // Vérifier si une taille est requise mais non sélectionnée
    if (sizeOptions.length > 0 && !selectedAttributes.size) {
      toast({
        title: translate('common.size_required'),
        description: translate('common.size_required_description'),
        variant: "destructive",
      });
      return;
    }
    
    setIsAddingToCart(true);
    
    try {
      // Si une variante est sélectionnée, créer un produit basé sur cette variante
      let productToAdd = product;
      
      if (selectedVariant) {
        // Créer une description des attributs sélectionnés
        const attributeDescriptions = [];
        
        if (selectedAttributes.color) {
          attributeDescriptions.push(`${translate('common.color')}: ${selectedAttributes.color}`);
        }
        
        if (selectedAttributes.size) {
          attributeDescriptions.push(`${translate('common.size')}: ${selectedAttributes.size}`);
        }
        
        if (selectedAttributes.material) {
          attributeDescriptions.push(`${translate('common.material')}: ${selectedAttributes.material}`);
        }
        
        const variantDescription = attributeDescriptions.join(', ');
        
        // Créer un tableau d'attributs combinés
        const combinedAttributes: ProductAttribute[] = [];
        
        // Ajouter les attributs sélectionnés
        if (selectedAttributes.color) {
          const colorAttr = selectedVariant.attributes.find(
            a => a.type === 'couleur' && 'nom' in a && a.nom === selectedAttributes.color
          );
          if (colorAttr) combinedAttributes.push(colorAttr);
        }
        
        if (selectedAttributes.size) {
          const sizeAttr = selectedVariant.attributes.find(
            a => a.type === 'taille' && 'valeur' in a && a.valeur === selectedAttributes.size
          );
          if (sizeAttr) combinedAttributes.push(sizeAttr);
        }
        
        if (selectedAttributes.material) {
          const materialAttr = selectedVariant.attributes.find(
            a => a.type === 'matiere' && 'valeur' in a && a.valeur === selectedAttributes.material
          );
          if (materialAttr) combinedAttributes.push(materialAttr);
        }
        
        // Créer un nouveau produit basé sur la variante
        productToAdd = new Product(
          `${product.id}-${selectedVariant.id}`,
          `${product.name} - ${variantDescription}`,
          product.slug,
          product.productCode,
          product.brand,
          product.description,
          selectedVariant.getTotalPrice(product.getCurrentPrice()),
          product.currency,
          product.discountPrice,
          product.discountStartDate,
          product.discountEndDate,
          selectedVariant.imageUrls.length > 0 ? selectedVariant.imageUrls[0] : product.imageUrl,
          selectedVariant.imageUrls.length > 0 ? selectedVariant.imageUrls : product.imageUrls,
          product.category,
          selectedVariant.inStock,
          product.rating,
          product.reviews,
          product.seller,
          selectedVariant.imageUrls.length > 0 ? selectedVariant.imageUrls.slice(0, 2) : product.mainImageUrls,
          selectedVariant.imageUrls.length > 0 ? selectedVariant.imageUrls.slice(2) : product.additionalImageUrls,
          combinedAttributes,
          []
        );
      }
      
      // Récupérer les informations de livraison si une zone est sélectionnée
      let deliveryInfo: DeliveryInfoType | undefined = undefined;
      
      if (selectedZone) {
        const deliveryData = await getDeliveryInfo(productToAdd);
        
        if (deliveryData && deliveryData.available && deliveryData.livraison) {
          const frais_livraison = Number(deliveryData.livraison.frais_livraison);
          const frais_specifiques = deliveryData.livraison.frais_livraison_specifique !== undefined ?
            Number(deliveryData.livraison.frais_livraison_specifique) : undefined;
          
          deliveryInfo = {
            frais_livraison: frais_livraison,
            delai_livraison_min: deliveryData.livraison.delai_livraison_min,
            delai_livraison_max: deliveryData.livraison.delai_livraison_max,
            frais_livraison_specifique: frais_specifiques,
            zone_id: selectedZone.id,
            zone_nom: selectedZone.nom
          };
        }
      }
      
      // Ajouter le produit au panier
      onAddToCart(productToAdd, quantity, deliveryInfo);
      
      // Fermer le modal
      onClose();
      
      // Afficher une notification
      toast({
        title: translate('common.added_to_cart'),
        description: translate('common.added_to_cart_description', {
          quantity: quantity,
          product: productToAdd.name,
          plural: quantity > 1 ? translate('common.added_to_cart_plural_many') : translate('common.added_to_cart_plural_one')
        }),
        variant: "success",
      });
    } catch (error) {
      console.error('Erreur lors de l\'ajout au panier:', error);
      toast({
        title: translate('common.error'),
        description: translate('common.add_to_cart_error'),
        variant: "destructive",
      });
    } finally {
      setIsAddingToCart(false);
    }
  };
  
  // Gérer le contact marchand
  const handleContactMerchant = () => {
    toast({
      title: translate('common.feature_in_development'),
      description: translate('common.contact_merchant_not_available'),
      variant: "default",
    });
    onClose();
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{product?.name}</DialogTitle>
        </DialogHeader>
        
        {isLoading ? (
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin mr-2" />
            <span>{translate('common.loading')}</span>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Image du produit */}
            <div className="flex justify-center">
              <img 
                src={product?.imageUrl} 
                alt={product?.name} 
                className="h-40 object-contain"
              />
            </div>
            
            {/* Prix */}
            <div className="flex items-center gap-2">
              <div className="text-lg font-semibold">{product?.formattedPrice()}</div>
              {product?.isOnSale() && (
                <>
                  <div className="text-sm text-muted-foreground line-through">
                    {product.formattedOriginalPrice()}
                  </div>
                  <div className="rounded-md bg-red-100 px-1.5 py-0.5 text-xs font-medium text-red-800">
                    {translate('common.discount_percentage', { percentage: product.getDiscountPercentage() || 0 })}
                  </div>
                </>
              )}
            </div>
            
            {/* Sélection des variantes */}
            {hasVariants && (
              <div className="space-y-3">
                {/* Sélection de couleur */}
                {colorOptions.length > 0 && (
                  <div>
                    <Label className="mb-2 block">{translate('common.color')}</Label>
                    <RadioGroup
                      value={selectedAttributes.color || ''}
                      onValueChange={(value) => setSelectedAttributes(prev => ({ ...prev, color: value }))}
                      className="flex flex-wrap gap-2"
                    >
                      {colorOptions.map(color => (
                        <div key={color} className="flex items-center space-x-2">
                          <RadioGroupItem value={color} id={`color-${color}`} />
                          <Label htmlFor={`color-${color}`}>{color}</Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>
                )}
                
                {/* Sélection de taille */}
                {sizeOptions.length > 0 && (
                  <div>
                    <Label className="mb-2 block">{translate('common.size')}</Label>
                    <RadioGroup
                      value={selectedAttributes.size || ''}
                      onValueChange={(value) => setSelectedAttributes(prev => ({ ...prev, size: value }))}
                      className="flex flex-wrap gap-2"
                    >
                      {sizeOptions.map(size => (
                        <div key={size} className="flex items-center space-x-2">
                          <RadioGroupItem value={size} id={`size-${size}`} />
                          <Label htmlFor={`size-${size}`}>{size}</Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>
                )}
                
                {/* Sélection de matière */}
                {materialOptions.length > 0 && (
                  <div>
                    <Label className="mb-2 block">{translate('common.material')}</Label>
                    <RadioGroup
                      value={selectedAttributes.material || ''}
                      onValueChange={(value) => setSelectedAttributes(prev => ({ ...prev, material: value }))}
                      className="flex flex-wrap gap-2"
                    >
                      {materialOptions.map(material => (
                        <div key={material} className="flex items-center space-x-2">
                          <RadioGroupItem value={material} id={`material-${material}`} />
                          <Label htmlFor={`material-${material}`}>{material}</Label>
                        </div>
                      ))}
                    </RadioGroup>
                  </div>
                )}
              </div>
            )}
            
            {/* Sélection de la zone de livraison */}
            {availableZones.length > 0 ? (
              <div>
                <ZoneLivraisonSelector />
                <DeliveryInfo product={product} className="mt-2" />
              </div>
            ) : (
              <div className="text-amber-600 text-sm">
                {translate('common.no_delivery_zones')}
              </div>
            )}
            
            {/* Quantité */}
            <div>
              <Label className="mb-2 block">{translate('common.quantity')}</Label>
              <div className="flex items-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQuantity(prev => (prev > 1 ? prev - 1 : 1))}
                  disabled={quantity <= 1}
                >
                  -
                </Button>
                <span className="mx-4 min-w-[2rem] text-center">{quantity}</span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQuantity(prev => prev + 1)}
                >
                  +
                </Button>
              </div>
            </div>
            
            {/* Boutons d'action */}
            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={onClose}>
                {translate('common.cancel')}
              </Button>
              
              {availableZones.length > 0 ? (
                <Button 
                  onClick={handleAddToCart} 
                  disabled={isAddingToCart || !product?.inStock}
                >
                  {isAddingToCart ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {translate('common.adding_to_cart')}
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="mr-2 h-4 w-4" />
                      {translate('common.add_to_cart')}
                    </>
                  )}
                </Button>
              ) : (
                <Button onClick={handleContactMerchant}>
                  {translate('common.contact_merchant')}
                </Button>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
