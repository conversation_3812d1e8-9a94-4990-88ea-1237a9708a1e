<?php

namespace App\Filament\Resources\MarchandResource\Pages;

use App\Filament\Resources\MarchandResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Marchand;

class ListMarchands extends ListRecords
{
    protected static string $resource = MarchandResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            'tous' => Tab::make('Tous les marchands')
                ->badge(Marchand::count()),
            'actifs' => Tab::make('Marchands actifs')
                ->badge(Marchand::whereHas('user', fn (Builder $query) => $query->where('is_active', true))->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->whereHas('user', fn (Builder $query) => $query->where('is_active', true))),
            'inactifs' => Tab::make('Marchands inactifs')
                ->badge(Marchand::whereHas('user', fn (Builder $query) => $query->where('is_active', false))->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->whereHas('user', fn (Builder $query) => $query->where('is_active', false))),
            'avec_produits' => Tab::make('Avec produits')
                ->badge(Marchand::has('produits')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->has('produits')),
            'sans_produits' => Tab::make('Sans produits')
                ->badge(Marchand::doesntHave('produits')->count())
                ->modifyQueryUsing(fn (Builder $query) => $query->doesntHave('produits')),
        ];
    }
}
