<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Review extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'produit_id',
        'user_id',
        'name',
        'email',
        'rating',
        'comment',
        'images',
        'likes',
        'dislikes',
        'ip_address',
        'is_approved',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'rating' => 'integer',
        'images' => 'array',
        'likes' => 'integer',
        'dislikes' => 'integer',
        'is_approved' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['image_urls', 'thumbnail_urls', 'formatted_date'];

    /**
     * Get the product that owns the review.
     */
    public function produit(): BelongsTo
    {
        return $this->belongsTo(Produit::class);
    }

    /**
     * Get the user that owns the review.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the votes for the review.
     */
    public function votes(): HasMany
    {
        return $this->hasMany(ReviewVote::class);
    }

    /**
     * Get the formatted date for humans (e.g., "il y a 2 jours").
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get the image URLs with full paths.
     */
    public function getImageUrlsAttribute(): array
    {
        if (empty($this->images)) {
            return [];
        }

        return array_map(function ($imageData) {
            $folder = $imageData['folder'] ?? '0';
            $name = $imageData['name'] ?? '';
            return url("/images/reviews/{$folder}/{$name}");
        }, $this->images);
    }

    /**
     * Get the thumbnail URLs for all review images.
     *
     * @return array
     */
    public function getThumbnailUrlsAttribute(): array
    {
        if (empty($this->images)) {
            return [
                'small' => [],
                'medium' => [],
                'large' => []
            ];
        }

        $thumbnails = [
            'small' => [],
            'medium' => [],
            'large' => []
        ];

        foreach ($this->images as $imageData) {
            $folder = $imageData['folder'] ?? '0';
            $name = $imageData['name'] ?? '';
            $imagePath = "reviews/{$folder}/{$name}";

            // Utiliser le helper pour obtenir les URLs des miniatures
            $thumbnails['small'][] = \App\Helpers\ThumbnailHelper::getThumbnailUrl($imagePath, 'small');
            $thumbnails['medium'][] = \App\Helpers\ThumbnailHelper::getThumbnailUrl($imagePath, 'medium');
            $thumbnails['large'][] = \App\Helpers\ThumbnailHelper::getThumbnailUrl($imagePath, 'large');
        }

        return $thumbnails;
    }
}
