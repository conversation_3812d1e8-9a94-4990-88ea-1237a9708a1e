import { useState, useEffect, useCallback } from 'react';
import { Head, router } from '@inertiajs/react';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import ProductListInfinite from '@/components/ecommerce/ProductListInfinite';
import SubcategoryFilter from '@/components/ecommerce/SubcategoryFilter';
import BreadcrumbTrail from '@/components/ecommerce/BreadcrumbTrail';
import { ProductService } from '@/services/ProductService';
import { CategoryService } from '@/services/CategoryService';
import { Product } from '@/models/Product';
import { Category } from '@/models/Category';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronDown, ChevronUp, Filter, Search, SlidersHorizontal } from 'lucide-react';
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from '@/components/ui/sheet';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useTranslation } from '@/hooks/use-translation';

/**
 * Props pour la page de catégorie
 */
interface CategoryPageProps {
    categorySlug: string;
}

/**
 * Page affichant les produits d'une catégorie spécifique
 *
 * @param categorySlug - Le slug de la catégorie à afficher
 */
export default function CategoryPage({ categorySlug }: CategoryPageProps) {
    const [category, setCategory] = useState<Category | null>(null);
    const [initialProducts, setInitialProducts] = useState<Product[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const { tDefault } = useTranslation();
    const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000000]);
    const [sortBy, setSortBy] = useState<string>('default');
    const [inStockOnly, setInStockOnly] = useState<boolean>(false);
    const [selectedRatings, setSelectedRatings] = useState<number[]>([]);
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [expandedFilters, setExpandedFilters] = useState<Record<string, boolean>>({
        price: true,
        rating: true,
        availability: true,
    });

    // État pour suivre les sous-catégories sélectionnées
    const [selectedSubcategories, setSelectedSubcategories] = useState<string[]>([]);

    // Récupérer les sous-catégories sélectionnées depuis l'URL au chargement initial
    useEffect(() => {
        const params = new URLSearchParams(window.location.search);
        const subcategories = params.get('subcategories')?.split(',').filter(Boolean) || [];
        setSelectedSubcategories(subcategories);
    }, []);

    // Récupération des données
    useEffect(() => {
        const productService = new ProductService();
        const categoryService = new CategoryService();

        const fetchData = async () => {
            try {
                setIsLoading(true);
                // Utiliser le slug pour récupérer la catégorie
                const categoryData = await categoryService.getCategoryBySlug(categorySlug);

                if (!categoryData) {
                    console.error(`Catégorie non trouvée: ${categorySlug}`);
                    return;
                }

                // Récupérer les produits de la catégorie principale (première page seulement)
                const result = await productService.getProductsByCategoryPaginated(categoryData.id, 1, 12);

                setCategory(categoryData);
                setInitialProducts(result.products);

                // Définir la plage de prix en fonction des produits
                if (result.products.length > 0) {
                    const prices = result.products.map(p => p.price);
                    const minPrice = Math.floor(Math.min(...prices));
                    const maxPrice = Math.ceil(Math.max(...prices));
                    setPriceRange([minPrice, maxPrice]);
                }
            } catch (error) {
                console.error('Erreur lors de la récupération des données:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [categorySlug]);

    // Fonction pour appliquer les filtres et récupérer de nouveaux produits
    const applyFilters = useCallback(async () => {
        if (!category) return;

        try {
            setIsLoading(true);
            const productService = new ProductService();

            // Créer un objet de filtres à envoyer à l'API
            const apiFilters: Record<string, string | number | boolean> = {
                sort_by: sortBy,
                min_price: priceRange[0],
                max_price: priceRange[1],
                in_stock: inStockOnly
            };

            // Ajouter le terme de recherche si nécessaire
            if (searchTerm) {
                apiFilters.search = searchTerm;
            }

            // Ajouter les sous-catégories sélectionnées si nécessaire
            if (selectedSubcategories.length > 0) {
                apiFilters.subcategories = selectedSubcategories.join(',');
            }

            // Ajouter les notations sélectionnées si nécessaire
            if (selectedRatings.length > 0) {
                apiFilters.min_rating = Math.min(...selectedRatings);
            }

            // Les filtres sont maintenant correctement transmis à l'API

            // Récupérer les produits filtrés directement depuis l'API
            const result = await productService.getProductsByCategoryPaginated(
                category.id,
                1,
                12,
                apiFilters
            );

            // Mettre à jour les produits initiaux avec les résultats filtrés
            setInitialProducts(result.products);
        } catch (error) {
            console.error('Erreur lors de l\'application des filtres:', error);
        } finally {
            setIsLoading(false);
        }
    }, [category, sortBy, priceRange, inStockOnly, selectedSubcategories, selectedRatings, searchTerm]);

    // Appliquer les filtres lorsqu'ils changent
    useEffect(() => {
        if (category) {
            applyFilters();
        }
    }, [applyFilters, category]);

    /**
     * Bascule l'état d'expansion d'une section de filtres
     *
     * @param section - La section à basculer
     */
    const toggleFilterSection = (section: string) => {
        setExpandedFilters(prev => ({
            ...prev,
            [section]: !prev[section]
        }));
    };

    /**
     * Gère le changement d'une notation
     *
     * @param rating - La notation à ajouter ou supprimer
     * @param checked - Si la notation est cochée ou non
     */
    const handleRatingChange = (rating: number, checked: boolean) => {
        if (checked) {
            // Ajouter la notation si elle n'est pas déjà présente
            setSelectedRatings(prev => prev.includes(rating) ? prev : [...prev, rating]);
        } else {
            // Supprimer la notation
            setSelectedRatings(prev => prev.filter(r => r !== rating));
        }
    };

    /**
     * Réinitialise tous les filtres
     */
    const resetFilters = () => {
        console.log("Réinitialisation des filtres");

        // Réinitialiser les sous-catégories sélectionnées
        setSelectedSubcategories([]);

        // Réinitialiser les autres filtres
        setSortBy('default');
        setInStockOnly(false);
        setSelectedRatings([]);
        setSearchTerm('');

        // Afficher l'indicateur de chargement
        setIsLoading(true);

        // Récupérer les produits sans filtres
        if (category) {
            const productService = new ProductService();
            productService.getProductsByCategoryPaginated(
                category.id,
                1,
                12,
                { sort_by: 'default' }
            ).then(result => {
                console.log('Produits réinitialisés :', result.products);
                setInitialProducts(result.products);

                // Réinitialiser le prix aux valeurs min/max des produits
                if (result.products.length > 0) {
                    const prices = result.products.map(p => p.price);
                    const minPrice = Math.floor(Math.min(...prices));
                    const maxPrice = Math.ceil(Math.max(...prices));
                    setPriceRange([minPrice, maxPrice]);
                } else {
                    // Valeurs par défaut si aucun produit n'est disponible
                    setPriceRange([0, 1000]);
                }

                setIsLoading(false);
            }).catch(error => {
                console.error('Erreur lors de la réinitialisation des produits:', error);
                setIsLoading(false);

                // Valeurs par défaut en cas d'erreur
                setPriceRange([0, 1000]);
            });
        } else {
            // Valeurs par défaut si aucune catégorie n'est disponible
            setPriceRange([0, 1000]);
            setIsLoading(false);
        }

        // Mettre à jour l'URL pour supprimer les paramètres de filtrage
        router.get(route('category', {
            categorySlug: category?.slug || ''
        }), {}, { preserveState: true, preserveScroll: true });
    };

    // Contenu des filtres
    const filterContent = (
        <div className="space-y-6">
            <div>
                <div className="mb-4 flex items-center justify-between">
                    <h3 className="text-lg font-medium">{tDefault('filters.title', 'Filtres')}</h3>
                    <Button variant="ghost" size="sm" onClick={resetFilters}>
                        {tDefault('filters.reset', 'Réinitialiser')}
                    </Button>
                </div>
                <Separator />
            </div>

            {/* Filtre de prix */}
            <div>
                <div
                    className="flex cursor-pointer items-center justify-between"
                    onClick={() => toggleFilterSection('price')}
                >
                    <h4 className="font-medium">{tDefault('filters.price_range', 'Prix')}</h4>
                    {expandedFilters.price ? (
                        <ChevronUp className="h-4 w-4" />
                    ) : (
                        <ChevronDown className="h-4 w-4" />
                    )}
                </div>

                {expandedFilters.price && (
                    <div className="mt-4 space-y-4">
                        <div className="flex items-center justify-between">
                            <div className="w-[45%]">
                                <Label htmlFor="min-price">{tDefault('filters.min', 'Min')}</Label>
                                <Input
                                    id="min-price"
                                    type="number"
                                    value={priceRange[0]}
                                    onChange={(e) => setPriceRange([Number(e.target.value), priceRange[1]])}
                                    min={initialProducts.length > 0 ? Math.floor(Math.min(...initialProducts.map(p => p.price))) : 0}
                                />
                            </div>
                            <span className="text-center">-</span>
                            <div className="w-[45%]">
                                <Label htmlFor="max-price">{tDefault('filters.max', 'Max')}</Label>
                                <Input
                                    id="max-price"
                                    type="number"
                                    value={priceRange[1]}
                                    onChange={(e) => setPriceRange([priceRange[0], Number(e.target.value)])}
                                    min={priceRange[0]}
                                    max={initialProducts.length > 0 ? Math.ceil(Math.max(...initialProducts.map(p => p.price)) * 1.5) : 10000}
                                />
                            </div>
                        </div>
                        <Slider
                            defaultValue={[priceRange[0], priceRange[1]]}
                            value={[priceRange[0], priceRange[1]]}
                            onValueChange={(value) => setPriceRange([value[0], value[1]])}
                            min={initialProducts.length > 0 ? Math.floor(Math.min(...initialProducts.map(p => p.price))) : 0}
                            max={initialProducts.length > 0 ? Math.ceil(Math.max(...initialProducts.map(p => p.price))) : 1000}
                            step={10}
                            className="py-4"
                        />
                    </div>
                )}
            </div>

            <Separator />

            {/* Filtre de disponibilité */}
            <div>
                <div
                    className="flex cursor-pointer items-center justify-between"
                    onClick={() => toggleFilterSection('availability')}
                >
                    <h4 className="font-medium">{tDefault('filters.availability', 'Disponibilité')}</h4>
                    {expandedFilters.availability ? (
                        <ChevronUp className="h-4 w-4" />
                    ) : (
                        <ChevronDown className="h-4 w-4" />
                    )}
                </div>

                {expandedFilters.availability && (
                    <div className="mt-4 space-y-2">
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="in-stock"
                                checked={inStockOnly}
                                onCheckedChange={(checked) => setInStockOnly(checked === true)}
                            />
                            <Label htmlFor="in-stock">{tDefault('filters.in_stock', 'En stock uniquement')}</Label>
                        </div>
                    </div>
                )}
            </div>

            <Separator />

            {/* Filtre de notation */}
            <div>
                <div
                    className="flex cursor-pointer items-center justify-between"
                    onClick={() => toggleFilterSection('rating')}
                >
                    <h4 className="font-medium">{tDefault('filters.rating', 'Notation')}</h4>
                    {expandedFilters.rating ? (
                        <ChevronUp className="h-4 w-4" />
                    ) : (
                        <ChevronDown className="h-4 w-4" />
                    )}
                </div>

                {expandedFilters.rating && (
                    <div className="mt-4 space-y-2">
                        {[4, 3, 2, 1].map((rating) => (
                            <div key={rating} className="flex items-center space-x-2">
                                <Checkbox
                                    id={`rating-${rating}`}
                                    checked={selectedRatings.includes(rating)}
                                    onCheckedChange={(checked) => handleRatingChange(rating, checked === true)}
                                />
                                <Label
                                    htmlFor={`rating-${rating}`}
                                    className="flex items-center cursor-pointer"
                                    onClick={() => handleRatingChange(rating, !selectedRatings.includes(rating))}
                                >
                                    {Array.from({ length: 5 }).map((_, i) => (
                                        <svg
                                            key={i}
                                            className={`h-4 w-4 ${i < rating ? 'text-amber-500 fill-current' : 'text-gray-300'}`}
                                            xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 24 24"
                                        >
                                            <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                                        </svg>
                                    ))}
                                    <span className="ml-1">{tDefault('filters.and_up', 'et plus')}</span>
                                </Label>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );

    return (
        <EcommerceLayout>
            <Head title={category ? `${category.getTranslatedName()} - ${tDefault('common.site_name', 'Lorrelei')}` : `${tDefault('breadcrumb.category', 'Catégorie')} - ${tDefault('common.site_name', 'Lorrelei')}`} />

            <div className="container mx-auto px-4 py-8">
                {/* Fil d'Ariane */}
                {category && (
                    <BreadcrumbTrail category={category} className="mb-4" />
                )}

                {/* En-tête de la catégorie */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold">{category ? category.getTranslatedName() : tDefault('breadcrumb.category', 'Catégorie')}</h1>
                    {category?.description && (
                        <p className="mt-2 text-muted-foreground">{category.getTranslatedDescription()}</p>
                    )}
                </div>

                <div className="mb-8 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                    {/* Barre de recherche */}
                    <form onSubmit={(e) => {
                        e.preventDefault();
                        applyFilters();
                    }} className="flex w-full max-w-md gap-2">
                        <Input
                            type="text"
                            placeholder={tDefault('common.search', 'Rechercher dans cette catégorie...')}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="flex-1"
                        />
                        <Button type="submit">
                            <Search className="mr-2 h-4 w-4" />
                            {tDefault('filters.apply', 'Rechercher')}
                        </Button>
                    </form>
                </div>

                <div className="lg:grid lg:grid-cols-4 lg:gap-8">
                    {/* Filtres (desktop) */}
                    <div className="hidden lg:block space-y-6">
                        {filterContent}

                        {/* Filtre de sous-catégories */}
                        {category && (
                            <SubcategoryFilter
                                categoryId={category.id}
                                categorySlug={category.slug}
                                className="mt-6"
                                selectedSubcategories={selectedSubcategories}
                                onReset={resetFilters}
                                onFilterChange={(subcategories) => {
                                    // Mettre à jour l'état des sous-catégories sélectionnées
                                    setSelectedSubcategories(subcategories);

                                    // Appliquer les filtres
                                    applyFilters();
                                }}
                            />
                        )}
                    </div>

                    {/* Produits */}
                    <div className="lg:col-span-3">
                        {/* Barre de tri et filtres (mobile) */}
                        <div className="mb-6 flex flex-wrap items-center justify-between gap-4">
                            <div className="flex items-center gap-2">
                                <Sheet>
                                    <SheetTrigger asChild>
                                        <Button variant="outline" size="sm" className="lg:hidden">
                                            <Filter className="mr-2 h-4 w-4" />
                                            {tDefault('filters.title', 'Filtres')}
                                        </Button>
                                    </SheetTrigger>
                                    <SheetContent side="left" className="w-[300px] sm:w-[350px]">
                                        {filterContent}
                                    </SheetContent>
                                </Sheet>

                                <p className="text-sm text-muted-foreground">
                                    {initialProducts.length} {initialProducts.length === 1
                                        ? tDefault('common.product', 'produit')
                                        : tDefault('common.products', 'produits')}
                                </p>
                            </div>

                            <div className="flex items-center gap-2">
                                <SlidersHorizontal className="h-4 w-4 text-muted-foreground" />
                                <Select value={sortBy} onValueChange={setSortBy}>
                                    <SelectTrigger className="w-[180px]">
                                        <SelectValue placeholder={tDefault('filters.sort_by', 'Trier par')} />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="default">{tDefault('filters.default', 'Pertinence')}</SelectItem>
                                        <SelectItem value="price-asc">{tDefault('filters.price_asc', 'Prix croissant')}</SelectItem>
                                        <SelectItem value="price-desc">{tDefault('filters.price_desc', 'Prix décroissant')}</SelectItem>
                                        <SelectItem value="name-asc">{tDefault('filters.name_asc', 'Nom (A-Z)')}</SelectItem>
                                        <SelectItem value="name-desc">{tDefault('filters.name_desc', 'Nom (Z-A)')}</SelectItem>
                                        <SelectItem value="rating-desc">{tDefault('filters.popularity', 'Meilleures notes')}</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>

                        {/* Liste des produits */}
                        {isLoading ? (
                            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3">
                                {Array.from({ length: 6 }).map((_, index) => (
                                    <div key={index} className="animate-pulse rounded-lg border">
                                        <div className="aspect-square bg-muted"></div>
                                        <div className="p-4 space-y-3">
                                            <div className="h-4 w-1/4 rounded bg-muted"></div>
                                            <div className="h-4 w-3/4 rounded bg-muted"></div>
                                            <div className="h-4 w-1/2 rounded bg-muted"></div>
                                            <div className="h-8 rounded bg-muted"></div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ) : (
                            <ProductListInfinite
                                key={`product-list-${selectedSubcategories.join('-')}-${sortBy}-${priceRange[0]}-${priceRange[1]}-${searchTerm}-${inStockOnly}-${selectedRatings.join('-')}`}
                                categoryId={category?.id}
                                initialProducts={initialProducts}
                                itemsPerPage={10}
                                emptyMessage={tDefault('pages.products.no_products_found', 'Aucun produit trouvé. Essayez de modifier vos filtres ou de revenir plus tard.')}
                                className="min-h-[400px]"
                                filters={{
                                    sort_by: sortBy,
                                    min_price: priceRange[0],
                                    max_price: priceRange[1],
                                    search: searchTerm,
                                    in_stock: inStockOnly,
                                    subcategories: selectedSubcategories.join(','),
                                    ...(selectedRatings.length > 0 && { min_rating: Math.min(...selectedRatings) })
                                }}
                                showPagination={true}
                            />
                        )}
                    </div>
                </div>
            </div>
        </EcommerceLayout>
    );
}
