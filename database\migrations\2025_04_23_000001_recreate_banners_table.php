<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Sauvegarder les données existantes
        $banners = DB::table('banners')->get();
        
        // Supprimer la table existante
        Schema::dropIfExists('banners');
        
        // Recréer la table avec les bons types
        Schema::create('banners', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('image_url', 255)->nullable();
            $table->string('target_url', 255)->nullable();
            $table->string('position', 100);
            $table->string('title', 255)->nullable();
            $table->text('description')->nullable();
            $table->string('button_text', 100)->nullable();
            $table->string('type', 50)->nullable();
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('priorite')->default(0);
            $table->timestamps();
        });
        
        // Réinsérer les données sauvegardées
        foreach ($banners as $banner) {
            // Convertir l'objet en tableau
            $data = (array) $banner;
            
            // Supprimer les clés qui commencent par \0 (caractères nuls)
            $cleanData = [];
            foreach ($data as $key => $value) {
                $cleanKey = preg_replace('/^\x00[^\x00]+\x00/', '', $key);
                $cleanData[$cleanKey] = $value;
            }
            
            // Insérer les données
            DB::table('banners')->insert($cleanData);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Pas de rollback pour cette migration de correction
    }
};
