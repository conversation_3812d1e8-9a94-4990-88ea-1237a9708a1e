<?php

namespace App\Filament\Traits;

use App\Helpers\ImageStorage;
use Filament\Forms\Components\FileUpload;
use Illuminate\Database\Eloquent\Model;
use Intervention\Image\Facades\Image;

trait HandlesImageStorage
{
    /**
     * Tailles de miniatures par type de contenu
     */
    protected static $thumbnailSizes = [
        'products' => [
            'small' => [150, 150],
            'medium' => [300, 300],
            'large' => [600, 600]
        ],
        'categories' => [
            'small' => [100, 100],
            'medium' => [200, 200],
            'large' => [400, 400]
        ],
        'banners' => [
            'small' => [300, 128],
            'medium' => [600, 257],
            'large' => [1200, 514]
        ],
        'reviews' => [
            'small' => [100, 100],
            'medium' => [200, 200],
            'large' => [400, 400]
        ]
    ];

    /**
     * Configure un composant FileUpload pour utiliser notre système de stockage basé sur l'ID
     *
     * @param FileUpload $component Le composant FileUpload
     * @param string $baseDir Le dossier de base (ex: 'products', 'categories', 'banners', 'reviews')
     * @return FileUpload Le composant configuré
     */
    public static function configureImageUpload(FileUpload $component, string $baseDir): FileUpload
    {
        return $component
            ->disk('public_images')
            ->visibility('public')
            ->directory($baseDir)
            ->saveUploadedFileUsing(function ($file, $record) use ($baseDir) {
                if (!$record || !$record->exists) {
                    // Si le record n'existe pas encore, stocker temporairement dans le dossier '0'
                    return $file->store("{$baseDir}/0", 'public_images');
                }

                // Déterminer le dossier basé sur l'ID
                $folderPrefix = ImageStorage::getFolderPrefix($record->id);
                $path = "{$baseDir}/{$folderPrefix}";

                // Créer le dossier s'il n'existe pas
                $fullPath = public_path("images/{$path}");
                if (!file_exists($fullPath)) {
                    mkdir($fullPath, 0755, true);
                }

                // Générer un nom de fichier unique et stocker le fichier
                $filename = $file->hashName();
                $file->storeAs($path, $filename, 'public_images');

                // Générer les miniatures
                self::generateThumbnails($fullPath . '/' . $filename, $baseDir, $folderPrefix, $filename);

                return "{$path}/{$filename}";
            });
    }

    /**
     * Génère les miniatures pour une image
     *
     * @param string $imagePath Chemin complet vers l'image originale
     * @param string $baseDir Le dossier de base (ex: 'products', 'categories', 'banners', 'reviews')
     * @param string $folderPrefix Le préfixe du dossier basé sur l'ID
     * @param string $filename Le nom du fichier
     * @return void
     */
    protected static function generateThumbnails(string $imagePath, string $baseDir, string $folderPrefix, string $filename): void
    {
        // Vérifier si le type de contenu a des tailles de miniatures définies
        if (!isset(self::$thumbnailSizes[$baseDir])) {
            return;
        }

        $sizes = self::$thumbnailSizes[$baseDir];

        foreach ($sizes as $sizeName => $dimensions) {
            // Créer le dossier de miniatures s'il n'existe pas
            $thumbnailDir = public_path("images/thumbnail/{$baseDir}/{$folderPrefix}/{$sizeName}");
            if (!file_exists($thumbnailDir)) {
                mkdir($thumbnailDir, 0755, true);
            }

            // Générer la miniature
            $thumbnailPath = $thumbnailDir . '/' . $filename;

            try {
                $img = Image::make($imagePath);

                // Redimensionner l'image en conservant les proportions
                $img->resize($dimensions[0], $dimensions[1], function ($constraint) {
                    $constraint->aspectRatio();
                    $constraint->upsize();
                });

                // Sauvegarder la miniature
                $img->save($thumbnailPath, 85); // Qualité 85%
            } catch (\Exception $e) {
                // Log l'erreur mais ne pas interrompre le processus
                \Log::error("Erreur lors de la génération de la miniature: " . $e->getMessage());
            }
        }
    }

    /**
     * Méthode à appeler après la création d'un enregistrement pour déplacer les images
     * du dossier temporaire vers le dossier basé sur l'ID
     *
     * @param Model $record L'enregistrement créé
     * @param string $baseDir Le dossier de base
     * @param string $imageField Le nom du champ d'image
     * @return void
     */
    public static function moveImagesAfterCreate(Model $record, string $baseDir, string $imageField = 'images'): void
    {
        $images = $record->{$imageField};
        if (empty($images)) {
            return;
        }

        $folderPrefix = ImageStorage::getFolderPrefix($record->id);
        $newPath = "{$baseDir}/{$folderPrefix}";

        // Créer le dossier s'il n'existe pas
        $fullPath = public_path("images/{$newPath}");
        if (!file_exists($fullPath)) {
            mkdir($fullPath, 0755, true);
        }

        // Si c'est un tableau d'images
        if (is_array($images)) {
            $newImages = [];
            foreach ($images as $image) {
                // Vérifier si l'image est dans le dossier temporaire
                if (strpos($image, "{$baseDir}/0/") === 0) {
                    $filename = basename($image);
                    $oldPath = public_path("images/{$image}");
                    $newFilePath = "{$newPath}/{$filename}";
                    $newFullPath = public_path("images/{$newFilePath}");

                    // Déplacer le fichier
                    if (file_exists($oldPath)) {
                        rename($oldPath, $newFullPath);
                        $newImages[] = $newFilePath;

                        // Générer les miniatures pour l'image déplacée
                        self::generateThumbnails($newFullPath, $baseDir, $folderPrefix, $filename);
                    } else {
                        $newImages[] = $image;
                    }
                } else {
                    $newImages[] = $image;
                }
            }

            // Mettre à jour l'enregistrement
            $record->{$imageField} = $newImages;
            $record->save();
        }
        // Si c'est une seule image
        else if (is_string($images) && strpos($images, "{$baseDir}/0/") === 0) {
            $filename = basename($images);
            $oldPath = public_path("images/{$images}");
            $newFilePath = "{$newPath}/{$filename}";
            $newFullPath = public_path("images/{$newFilePath}");

            // Déplacer le fichier
            if (file_exists($oldPath)) {
                rename($oldPath, $newFullPath);
                $record->{$imageField} = $newFilePath;
                $record->save();

                // Générer les miniatures pour l'image déplacée
                self::generateThumbnails($newFullPath, $baseDir, $folderPrefix, $filename);
            }
        }
    }

    /**
     * Obtient l'URL d'une miniature
     *
     * @param string $imagePath Chemin de l'image originale
     * @param string $baseDir Le dossier de base
     * @param string $size La taille de la miniature (small, medium, large)
     * @return string|null L'URL de la miniature ou null si elle n'existe pas
     */
    public static function getThumbnailUrl(string $imagePath, string $baseDir, string $size = 'medium'): ?string
    {
        // Extraire les informations du chemin de l'image
        $pathParts = explode('/', $imagePath);
        $filename = array_pop($pathParts);
        $folderPrefix = array_pop($pathParts);

        // Construire le chemin de la miniature
        $thumbnailPath = "thumbnail/{$baseDir}/{$folderPrefix}/{$size}/{$filename}";
        $fullThumbnailPath = public_path("images/{$thumbnailPath}");

        // Vérifier si la miniature existe
        if (file_exists($fullThumbnailPath)) {
            return url("images/{$thumbnailPath}");
        }

        // Si la miniature n'existe pas, retourner l'image originale
        return url("images/{$imagePath}");
    }

    /**
     * Supprime toutes les miniatures d'une image
     *
     * @param string $imagePath Chemin de l'image originale
     * @param string $baseDir Le dossier de base
     * @return void
     */
    public static function deleteThumbnails(string $imagePath, string $baseDir): void
    {
        if (!isset(self::$thumbnailSizes[$baseDir])) {
            return;
        }

        // Extraire les informations du chemin de l'image
        $pathParts = explode('/', $imagePath);
        $filename = array_pop($pathParts);
        $folderPrefix = array_pop($pathParts);

        $sizes = self::$thumbnailSizes[$baseDir];

        foreach ($sizes as $sizeName => $dimensions) {
            $thumbnailPath = public_path("images/thumbnail/{$baseDir}/{$folderPrefix}/{$sizeName}/{$filename}");
            if (file_exists($thumbnailPath)) {
                unlink($thumbnailPath);
            }
        }
    }
}
