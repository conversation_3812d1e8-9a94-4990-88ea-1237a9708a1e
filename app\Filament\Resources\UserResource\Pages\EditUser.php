<?php

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use App\Models\User;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        /** @var User $user */
        $user = $this->record;

        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informations d\'utilisateur')
                    ->schema([
                        Infolists\Components\TextEntry::make('name')
                            ->label('Nom'),
                        Infolists\Components\TextEntry::make('email')
                            ->label('Email'),
                        Infolists\Components\TextEntry::make('role')
                            ->label('Rôle')
                            ->badge()
                            ->color(fn (string $state): string => match ($state) {
                                'Admin' => 'danger',
                                'Marchand' => 'warning',
                                'Client' => 'success',
                                default => 'gray',
                            }),
                        Infolists\Components\TextEntry::make('is_active')
                            ->label('Actif')
                            ->badge()
                            ->color(fn (bool $state): string => $state ? 'success' : 'danger')
                            ->formatStateUsing(fn (bool $state): string => $state ? 'Oui' : 'Non'),
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Créé le')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('last_login_at')
                            ->label('Dernière connexion')
                            ->dateTime(),
                    ])
                    ->columns(2),

                // Informations client (visible uniquement pour les clients)
                Infolists\Components\Section::make('Informations client')
                    ->schema([
                        Infolists\Components\TextEntry::make('client.prenom')
                            ->label('Prénom'),
                        Infolists\Components\TextEntry::make('client.nom')
                            ->label('Nom'),
                        Infolists\Components\TextEntry::make('client.telephone')
                            ->label('Téléphone'),
                        Infolists\Components\TextEntry::make('client.dateDeNaissance')
                            ->label('Date de naissance')
                            ->date(),
                    ])
                    ->columns(2)
                    ->visible(fn () => $user->role === 'Client' && $user->client),

                // Informations marchand (visible uniquement pour les marchands)
                Infolists\Components\Section::make('Informations marchand')
                    ->schema([
                        Infolists\Components\TextEntry::make('marchand.nomEntreprise')
                            ->label('Nom de l\'entreprise'),
                        Infolists\Components\TextEntry::make('marchand.siret')
                            ->label('SIRET'),
                        Infolists\Components\TextEntry::make('marchand.telephone')
                            ->label('Téléphone'),
                        Infolists\Components\TextEntry::make('marchand.siteWeb')
                            ->label('Site web')
                            ->url(),
                    ])
                    ->columns(2)
                    ->visible(fn () => $user->role === 'Marchand' && $user->marchand),

                // Adresses
                Infolists\Components\Section::make('Adresses')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('adresses')
                            ->schema([
                                Infolists\Components\TextEntry::make('type')
                                    ->label('Type')
                                    ->badge()
                                    ->color(fn (string $state): string => match ($state) {
                                        'Livraison' => 'success',
                                        'Facturation' => 'warning',
                                        'Entreprise' => 'info',
                                        default => 'gray',
                                    }),
                                Infolists\Components\TextEntry::make('rue')
                                    ->label('Rue'),
                                Infolists\Components\TextEntry::make('codePostal')
                                    ->label('Code postal'),
                                Infolists\Components\TextEntry::make('ville')
                                    ->label('Ville'),
                                Infolists\Components\TextEntry::make('etat')
                                    ->label('État/Province'),
                                Infolists\Components\TextEntry::make('pays')
                                    ->label('Pays'),
                            ])
                            ->columns(3),
                    ])
                    ->visible(fn () => $user->adresses->count() > 0),
            ]);
    }
}
