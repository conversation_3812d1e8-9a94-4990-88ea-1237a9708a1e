import { useState, useEffect } from 'react';

// Type pour les devises supportées
type CurrencyCode = 'FCFA' | 'EUR' | 'USD' | 'GBP' | 'XAF' | 'XOF';

// Type pour une devise
interface Currency {
  code: CurrencyCode;
  symbol: string;
}

// Mapping des codes de devise vers les symboles
const currencySymbols: Record<CurrencyCode, string> = {
  'FCFA': 'FCFA',
  'EUR': '€',
  'USD': '$',
  'GBP': '£',
  'XAF': 'FCFA',
  'XOF': 'FCFA',
};

/**
 * Hook pour gérer la devise actuelle
 */
export function useCurrency() {
  // Récupérer la devise actuelle depuis localStorage ou utiliser FCFA par défaut
  const [currentCurrency, setCurrentCurrency] = useState<CurrencyCode>(() => {
    if (typeof window !== 'undefined') {
      const storedCurrency = localStorage.getItem('currency') as CurrencyCode;
      return (storedCurrency && Object.keys(currencySymbols).includes(storedCurrency)) 
        ? storedCurrency 
        : 'FCFA';
    }
    return 'FCFA';
  });

  // Mettre à jour localStorage lorsque la devise change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('currency', currentCurrency);
    }
  }, [currentCurrency]);

  /**
   * Obtient le symbole de la devise actuelle
   */
  const getCurrentSymbol = (): string => {
    return currencySymbols[currentCurrency] || 'FCFA';
  };

  /**
   * Formate un prix avec la devise actuelle
   * 
   * @param price - Le prix à formater
   * @param customCurrency - Code de devise personnalisé (optionnel)
   */
  const formatPrice = (price: number, customCurrency?: CurrencyCode): string => {
    const currency = customCurrency || currentCurrency;
    const symbol = currencySymbols[currency] || 'FCFA';
    return `${price.toFixed(0)} ${symbol}`;
  };

  /**
   * Convertit un prix d'une devise à une autre
   * Note: Cette fonction est simplifiée et ne fait pas de vraie conversion
   * Pour une implémentation réelle, il faudrait utiliser des taux de change à jour
   * 
   * @param price - Le prix à convertir
   * @param fromCurrency - La devise source
   * @param toCurrency - La devise cible
   */
  const convertPrice = (
    price: number, 
    fromCurrency: CurrencyCode = 'FCFA', 
    toCurrency: CurrencyCode = currentCurrency
  ): number => {
    // Implémentation simplifiée - dans une vraie application, 
    // vous utiliseriez des taux de change réels
    if (fromCurrency === toCurrency) return price;
    
    // Taux de change approximatifs par rapport au FCFA
    const rates: Record<CurrencyCode, number> = {
      'FCFA': 1,
      'XAF': 1,
      'XOF': 1,
      'EUR': 0.0015,
      'USD': 0.0017,
      'GBP': 0.0013,
    };
    
    // Convertir d'abord en FCFA si nécessaire
    let valueInFCFA = price;
    if (fromCurrency !== 'FCFA') {
      valueInFCFA = price / rates[fromCurrency];
    }
    
    // Puis convertir de FCFA à la devise cible
    return valueInFCFA * rates[toCurrency];
  };

  return {
    currentCurrency,
    setCurrentCurrency,
    getCurrentSymbol,
    formatPrice,
    convertPrice,
  };
}
