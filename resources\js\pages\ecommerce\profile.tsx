import { useState, useEffect } from 'react';
import { Head, useForm } from '@inertiajs/react';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Home, MapPin, Package, Plus, ShoppingBag, Trash, User } from 'lucide-react';
import { format } from 'date-fns/format';
import { fr } from 'date-fns/locale/fr';
import { useToast } from '@/hooks/use-toast';
import { ButtonLoader } from '@/components/ui/loader';
import { useTranslation } from '@/hooks/use-translation';

interface ProfileProps {
    client: any;
    adresses: any[];
    commandes: any[];
}

export default function Profile({ client, adresses, commandes }: ProfileProps) {
    const [activeTab, setActiveTab] = useState('personal-info');
    const [isAddressDialogOpen, setIsAddressDialogOpen] = useState(false);
    const [editingAddress, setEditingAddress] = useState<any>(null);
    const { toast } = useToast();
    const { translate } = useTranslation();
    // Afficher les messages flash de la session
    useEffect(() => {
        // @ts-ignore
        const success = window.flash?.success;
        // @ts-ignore
        const error = window.flash?.error;
        // @ts-ignore
        const message = window.flash?.message;

        if (success) {
            toast({
                title: "Succès",
                description: success,
                variant: "success",
            });
        }

        if (error) {
            toast({
                title: "Erreur",
                description: error,
                variant: "destructive",
            });
        }

        if (message) {
            toast({
                title: "Information",
                description: message,
                variant: "default",
            });

            // Si le message concerne les adresses, activer l'onglet adresses
            if (message.includes('adresse')) {
                setActiveTab('addresses');
            }
        }
    }, []);
    // Formulaire pour les informations personnelles
    let formattedDate = '';
    if (client?.dateDeNaissance) {
        try {
            // Si la date est au format timestamp ou autre format, la convertir en YYYY-MM-DD
            const date = new Date(client.dateDeNaissance);
            if (!isNaN(date.getTime())) {
                formattedDate = date.toISOString().split('T')[0];
                console.log("Date formatée dans useEffect:", formattedDate);
            }
        } catch (e) {
            console.error("Erreur lors du formatage de la date dans useEffect:", e);
        }
    }
    const { data, setData, post, processing, errors, reset } = useForm({
        name: client?.user?.name || '',
        email: client?.user?.email || '',
        prenom: client?.prenom || '',
        nom: client?.nom || '',
        telephone: client?.telephone || '',
        dateDeNaissance: formattedDate || '',
    });

    // Formulaire pour les adresses
    const addressForm = useForm({
        rue: '',
        ville: '',
        etat: '',
        pays: '',
        codePostal: '',
        type: 'Livraison',
    });

    // Gérer la soumission du formulaire d'informations personnelles
    const handleSubmitPersonalInfo = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('my-account.personal-info.update'), {
            onSuccess: () => {
                // Afficher un message de succès
                toast({
                    title: translate('pages.profile.success'),
                    description: translate('pages.profile.success_update_profile_message'),
                });
            },
            onError: () => {
                toast({
                    title: translate('pages.profile.error'),
                    description: translate('pages.profile.update_profile_error'),
                    variant: 'destructive',
                });
            }
        });
    };

    // Gérer la soumission du formulaire d'adresse
    const handleSubmitAddress = (e: React.FormEvent) => {
        e.preventDefault();

        // S'assurer que toutes les données sont des chaînes de caractères
        const formattedData = {
            rue: String(addressForm.data.rue || ''),
            ville: String(addressForm.data.ville || ''),
            etat: String(addressForm.data.etat || ''),
            pays: String(addressForm.data.pays || ''),
            codePostal: String(addressForm.data.codePostal || ''),
            type: String(addressForm.data.type || 'Livraison'),
        };

        console.log('Données du formulaire avant envoi:', formattedData);

        addressForm.setData(formattedData);

        if (editingAddress) {
            // Mettre à jour une adresse existante
            addressForm.put(route('my-account.addresses.update', { id: editingAddress.id }), {
                onSuccess: () => {
                    toast({
                        title: 'Succès',
                        description: translate('pages.profile.address_updated'),
                    });
                    setIsAddressDialogOpen(false);
                    setEditingAddress(null);
                    addressForm.reset();
                },
                onError: () => {
                    toast({
                        title: translate('pages.profile.error'),
                        description: translate('pages.profile.address_error'),
                        variant: 'destructive',
                    });
                }
            });
        } else {
            // Ajouter une nouvelle adresse
            addressForm.post(route('my-account.addresses.add'), {
                onSuccess: () => {
                    toast({
                        title: 'Succès',
                        description: translate('pages.profile.address_added'),
                    });
                    setIsAddressDialogOpen(false);
                    addressForm.reset();
                },
                onError: () => {
                    toast({
                        title: translate('pages.profile.error'),
                        description: translate('pages.profile.address_error'),
                        variant: 'destructive',
                    });
                }
            });
        }
    };

    // Ouvrir le dialogue d'édition d'adresse
    const openEditAddressDialog = (address: any) => {
        setEditingAddress(address);
        addressForm.setData({
            rue: String(address.rue || ''),
            ville: String(address.ville || ''),
            etat: String(address.etat || ''),
            pays: String(address.pays || ''),
            codePostal: String(address.codePostal || ''),
            type: String(address.type || 'Livraison'),
        });
        setIsAddressDialogOpen(true);
    };

    // Définir une adresse comme adresse par défaut
    const setDefaultAddress = (addressId: number) => {
        post(route('my-account.addresses.default', { id: addressId }), {
            onSuccess: () => {
                toast({
                    title: translate('pages.profile.success'),
                    description: translate('pages.profile.set_as_default_success'),
                });
            },onError:()=>{
                toast({
                title: translate('pages.profile.error'),
                description: translate('pages.profile.set_as_default_error'),
                variant: 'destructive',
            });
            }
        });
    };

    // Supprimer une adresse
    const deleteAddress = (addressId: number) => {
        if (confirm('Êtes-vous sûr de vouloir supprimer cette adresse ?')) {
            post(route('my-account.addresses.delete', { id: addressId }), {
                method: 'delete',
                onSuccess:()=>{
                    toast({
                    title: translate('pages.profile.success'),
                    description: translate('pages.profile.address_deleted'),
                });
                },
                onError:()=>{
                    toast({
                    title: translate('pages.profile.error'),
                    description: translate('pages.profile.address_delete_error'),
                    variant: 'destructive',
                });
                }
            });
        }
    };

    // Formater le statut de la commande
    const formatOrderStatus = (status: string) => {
        const statusMap: Record<string, { label: string; color: string }> = {
            'EnAttente': { label: 'En attente', color: 'bg-yellow-100 text-yellow-800' },
            'EnCoursDeTraitement': { label: 'En cours de traitement', color: 'bg-blue-100 text-blue-800' },
            'Expédié': { label: 'Expédié', color: 'bg-purple-100 text-purple-800' },
            'Livré': { label: 'Livré', color: 'bg-green-100 text-green-800' },
            'Annulé': { label: 'Annulé', color: 'bg-red-100 text-red-800' },
            'Remboursé': { label: 'Remboursé', color: 'bg-gray-100 text-gray-800' },
        };

        const status_info = statusMap[status] || { label: status, color: 'bg-gray-100 text-gray-800' };

        return (
            <Badge className={`${status_info.color}`}>
                {status_info.label}
            </Badge>
        );
    };

    // Formater la date
    const formatDate = (date: string) => {
        if (!date) return '';
        return format(new Date(date), 'dd MMMM yyyy', { locale: fr });
    };

    return (
        <EcommerceLayout>
            <Head title={translate('pages.profile.title')}  />

            <div className="container mx-auto max-w-7xl px-4 py-8">
                <h1 className="mb-6 text-3xl font-bold">{translate('pages.profile.my_account')}</h1>

                <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                    <TabsList className="mb-8 grid w-full grid-cols-3">
                        <TabsTrigger value="personal-info" className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            <span className="hidden sm:inline">{translate('pages.profile.personal_info')}</span>
                            <span className="sm:hidden">Infos</span>
                        </TabsTrigger>
                        <TabsTrigger value="addresses" className="flex items-center gap-2">
                            <MapPin className="h-4 w-4" />
                            <span className="hidden sm:inline">{translate('pages.profile.addresses')}</span>
                            <span className="sm:hidden">Adresses</span>
                        </TabsTrigger>
                        <TabsTrigger value="orders" className="flex items-center gap-2">
                            <Package className="h-4 w-4" />
                            <span className="hidden sm:inline">{translate('pages.profile.orders')}</span>
                            <span className="sm:hidden">Commandes</span>
                        </TabsTrigger>
                    </TabsList>

                    {/* Onglet Informations personnelles */}
                    <TabsContent value="personal-info">
                        <Card>
                            <CardHeader>
                                <CardTitle>{translate('pages.profile.personal_info')}</CardTitle>
                                <CardDescription>
                                    {translate('pages.profile.personal_info_description')}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmitPersonalInfo} className="space-y-6">
                                    <div className="grid gap-6 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="prenom">{translate('pages.profile.first_name')}</Label>
                                            <Input
                                                id="prenom"
                                                value={data.prenom}
                                                onChange={(e) => setData('prenom', e.target.value)}
                                                required
                                            />
                                            {errors.prenom && (
                                                <p className="text-sm text-red-500">{errors.prenom}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="nom">{translate('pages.profile.last_name')}</Label>
                                            <Input
                                                id="nom"
                                                value={data.nom}
                                                onChange={(e) => setData('nom', e.target.value)}
                                                required
                                            />
                                            {errors.nom && (
                                                <p className="text-sm text-red-500">{errors.nom}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="email">{translate('pages.profile.email')}</Label>
                                            <Input
                                                id="email"
                                                type="email"
                                                value={data.email}
                                                onChange={(e) => setData('email', e.target.value)}
                                                required
                                            />
                                            {errors.email && (
                                                <p className="text-sm text-red-500">{errors.email}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="telephone">{translate('pages.profile.phone')}</Label>
                                            <Input
                                                id="telephone"
                                                value={data.telephone}
                                                onChange={(e) => setData('telephone', e.target.value)}
                                            />
                                            {errors.telephone && (
                                                <p className="text-sm text-red-500">{errors.telephone}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="dateDeNaissance">{translate('pages.profile.birth_date')}</Label>
                                            <Input
                                                id="dateDeNaissance"
                                                type="date"
                                                value={data.dateDeNaissance}
                                                onChange={(e) => setData('dateDeNaissance', e.target.value)}
                                            />
                                            {errors.dateDeNaissance && (
                                                <p className="text-sm text-red-500">{errors.dateDeNaissance}</p>
                                            )}
                                        </div>
                                    </div>

                                    <Button type="submit" disabled={processing}>
                                        {processing && <ButtonLoader />}
                                        {translate('pages.profile.save_changes')}
                                    </Button>
                                </form>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Onglet Adresses */}
                    <TabsContent value="addresses">
                        <div className="mb-4 flex items-center justify-between">
                            <h2 className="text-xl font-semibold">{translate('pages.profile.addresses')}</h2>
                            <Button onClick={() => {
                                setEditingAddress(null);
                                addressForm.reset();
                                setIsAddressDialogOpen(true);
                            }}>
                                <Plus className="mr-2 h-4 w-4" />
                                {translate('pages.profile.add_address')}
                            </Button>
                        </div>

                        {adresses.length === 0 ? (
                            <Card>
                                <CardContent className="flex flex-col items-center justify-center p-6">
                                    <MapPin className="mb-2 h-12 w-12 text-muted-foreground" />
                                    <p className="mb-4 text-center text-muted-foreground">
                                        {translate('pages.profile.no_addresses')}
                                    </p>
                                    <Button onClick={() => {
                                        setEditingAddress(null);
                                        addressForm.reset();
                                        setIsAddressDialogOpen(true);
                                    }}>
                                        <Plus className="mr-2 h-4 w-4" />
                                        {translate('pages.profile.add_address')}
                                    </Button>
                                </CardContent>
                            </Card>
                        ) : (
                            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                {adresses.map((address) => (
                                    <Card key={address.id} className="relative">
                                        {client?.adresse_id === address.id && (
                                            <Badge className="absolute right-2 top-2 bg-green-100 text-green-800">
                                                {translate('pages.profile.default_address')}
                                            </Badge>
                                        )}
                                        <CardHeader>
                                            <CardTitle className="flex items-center gap-2">
                                                {address.type === 'Livraison' && <Home className="h-5 w-5" />}
                                                {address.type === 'Facturation' && <ShoppingBag className="h-5 w-5" />}
                                                {address.type === 'Entreprise' && <MapPin className="h-5 w-5" />}
                                                {address.type === 'Livraison' ? translate('pages.profile.delivery_address') :
                                                    address.type === 'Facturation' ? translate('pages.profile.billing_address') :
                                                        address.type === 'Entreprise' ? translate('pages.profile.business_address') : address.type}
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <p>{address.rue}</p>
                                            <p>{address.codePostal} {address.ville}</p>
                                            <p>{address.etat}</p>
                                            <p>{address.pays}</p>
                                        </CardContent>
                                        <CardFooter className="flex justify-between">
                                            <div className="flex gap-2">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => openEditAddressDialog(address)}
                                                >
                                                    {translate('pages.profile.edit')}
                                                </Button>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    onClick={() => deleteAddress(address.id)}
                                                >
                                                    <Trash className="h-4 w-4" />
                                                </Button>
                                            </div>
                                            {client?.adresse_id !== address.id && (
                                                <Button
                                                    variant="secondary"
                                                    size="sm"
                                                    onClick={() => setDefaultAddress(address.id)}
                                                >
                                                    {translate('pages.profile.set_as_default')}
                                                </Button>
                                            )}
                                        </CardFooter>
                                    </Card>
                                ))}
                            </div>
                        )}

                        {/* Dialogue d'ajout/modification d'adresse */}
                        <Dialog open={isAddressDialogOpen} onOpenChange={setIsAddressDialogOpen}>
                            <DialogContent>
                                <DialogHeader>
                                    <DialogTitle>
                                        {editingAddress ? translate('pages.profile.edit_address') : translate('pages.profile.add_address')}
                                    </DialogTitle>
                                    <DialogDescription>
                                        {editingAddress
                                            ? translate('pages.profile.edit_address_description')
                                            : translate('pages.profile.add_address_description')}
                                    </DialogDescription>
                                </DialogHeader>

                                <form onSubmit={handleSubmitAddress} className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="type">{translate('pages.profile.address_type')}</Label>
                                        <Select
                                            value={addressForm.data.type}
                                            onValueChange={(value) => addressForm.setData('type', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Sélectionnez un type d'adresse" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="Livraison">{translate('pages.profile.delivery_address')}</SelectItem>
                                                <SelectItem value="Facturation">{translate('pages.profile.billing_address')}</SelectItem>
                                                <SelectItem value="Entreprise">{translate('pages.profile.business_address')}</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {addressForm.errors.type && (
                                            <p className="text-sm text-red-500">{addressForm.errors.type}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="rue">{translate('pages.profile.street')}</Label>
                                        <Textarea
                                            id="rue"
                                            value={addressForm.data.rue}
                                            onChange={(e) => addressForm.setData('rue', String(e.target.value))}
                                            required
                                        />
                                        {addressForm.errors.rue && (
                                            <p className="text-sm text-red-500">{addressForm.errors.rue}</p>
                                        )}
                                    </div>

                                    <div className="grid gap-4 md:grid-cols-2">
                                        <div className="space-y-2">
                                            <Label htmlFor="codePostal">{translate('pages.profile.postal_code')}</Label>
                                            <Input
                                                id="codePostal"
                                                value={addressForm.data.codePostal}
                                                onChange={(e) => addressForm.setData('codePostal', String(e.target.value))}
                                                
                                            />
                                            {addressForm.errors.codePostal && (
                                                <p className="text-sm text-red-500">{addressForm.errors.codePostal}</p>
                                            )}
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="ville">{translate('pages.profile.city')}</Label>
                                            <Input
                                                id="ville"
                                                value={addressForm.data.ville}
                                                onChange={(e) => addressForm.setData('ville', String(e.target.value))}
                                                required
                                            />
                                            {addressForm.errors.ville && (
                                                <p className="text-sm text-red-500">{addressForm.errors.ville}</p>
                                            )}
                                        </div>
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="etat">{translate('pages.profile.state')}</Label>
                                        <Input
                                            id="etat"
                                            value={addressForm.data.etat}
                                            onChange={(e) => addressForm.setData('etat', String(e.target.value))}
                                            required
                                        />
                                        {addressForm.errors.etat && (
                                            <p className="text-sm text-red-500">{addressForm.errors.etat}</p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="pays">{translate('pages.profile.country')}</Label>
                                        <Input
                                            id="pays"
                                            value={addressForm.data.pays}
                                            onChange={(e) => addressForm.setData('pays', String(e.target.value))}
                                            required
                                        />
                                        {addressForm.errors.pays && (
                                            <p className="text-sm text-red-500">{addressForm.errors.pays}</p>
                                        )}
                                    </div>

                                    <DialogFooter>
                                        <Button type="submit" disabled={addressForm.processing}>
                                            {addressForm.processing && <ButtonLoader />}
                                            {editingAddress ?  translate('pages.profile.save_changes') : translate('pages.profile.add_address')}
                                        </Button>
                                    </DialogFooter>
                                </form>
                            </DialogContent>
                        </Dialog>
                    </TabsContent>

                    {/* Onglet Commandes */}
                    <TabsContent value="orders">
                        <Card>
                            <CardHeader>
                                <CardTitle>{translate('pages.profile.order_history')}</CardTitle>
                                <CardDescription>
                                    {translate('pages.profile.order_history_description')}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {commandes.length === 0 ? (
                                    <div className="flex flex-col items-center justify-center py-8">
                                        <Package className="mb-2 h-12 w-12 text-muted-foreground" />
                                        <p className="text-center text-muted-foreground">
                                            {translate('pages.profile.no_orders')}
                                        </p>
                                    </div>
                                ) : (
                                    <div className="space-y-6">
                                        {commandes.map((commande) => (
                                            <Card key={commande.id} className="overflow-hidden">
                                                <CardHeader className="bg-muted/50 pb-2">
                                                    <div className="flex flex-wrap items-center justify-between gap-2">
                                                        <div>
                                                            <p className="text-sm text-muted-foreground">
                                                                {translate('pages.profile.order_number', { number: commande.id.substring(0, 8) })}
                                                            </p>
                                                            <p className="text-sm text-muted-foreground">
                                                                {formatDate(commande.creeLe)}
                                                            </p>
                                                        </div>
                                                        <div className="flex items-center gap-2">
                                                            <p className="font-medium">
                                                                {new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'FCFA' }).format(commande.montantTotal)}
                                                            </p>
                                                            {formatOrderStatus(commande.statut)}
                                                        </div>
                                                    </div>
                                                </CardHeader>
                                                <CardContent className="pt-4">
                                                    <div className="space-y-4">
                                                        {commande.articleCommandes.map((article: any) => (
                                                            <div key={article.id} className="flex items-center gap-4">
                                                                <div className="h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border">
                                                                    {article.produit.images && article.produit.images.length > 0 && (
                                                                        <img
                                                                            src={`/images/products/${article.produit.images[0]}`}
                                                                            alt={article.produit.nom}
                                                                            className="h-full w-full object-cover"
                                                                        />
                                                                    )}
                                                                </div>
                                                                <div className="flex-1">
                                                                    <h4 className="font-medium">{article.produit.nom}</h4>
                                                                    <p className="text-sm text-muted-foreground">
                                                                        {translate('pages.profile.quantity', {
                                                                            quantity: article.quantite,
                                                                            price: new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(article.prixUnitaire)
                                                                        })}
                                                                    </p>
                                                                </div>
                                                            </div>
                                                        ))}
                                                    </div>

                                                    {commande.adresse && (
                                                        <div className="mt-4 rounded-md bg-muted p-3">
                                                            <p className="mb-1 text-sm font-medium">{translate('pages.profile.shipping_address')}</p>
                                                            <p className="text-sm">{commande.adresse.rue}</p>
                                                            <p className="text-sm">{commande.adresse.codePostal} {commande.adresse.ville}</p>
                                                            <p className="text-sm">{commande.adresse.pays}</p>
                                                        </div>
                                                    )}

                                                    {commande.codeSuivi && (
                                                        <div className="mt-4">
                                                            <p className="text-sm font-medium">{translate('pages.profile.tracking_number', { number: commande.codeSuivi })}</p>
                                                        </div>
                                                    )}
                                                </CardContent>
                                            </Card>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </EcommerceLayout>
    );
}
