<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Categorie;
use App\Models\Marchand;
use App\Models\Produit;

class ProduitFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Produit::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'marchand_id' => Marchand::factory(),
            'nom' => fake()->regexify('[A-Za-z0-9]{255}'),
            'description' => fake()->text(),
            'prix' => fake()->randomFloat(2, 0, 99999999.99),
            'stock' => fake()->numberBetween(-10000, 10000),
            'images' => '{}',
            'categorie_id' => Categorie::factory(),
            'creeLe' => fake()->dateTime(),
            'misAJourLe' => fake()->dateTime(),
            'poids' => fake()->randomFloat(2, 0, 999999.99),
            'dimensions' => '{}',
            'discount_price' => fake()->randomFloat(2, 0, 99999999.99),
            'discount_start_date' => fake()->dateTime(),
            'discount_end_date' => fake()->dateTime(),
        ];
    }
}
