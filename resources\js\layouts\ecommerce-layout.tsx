import { ReactNode } from 'react';
import EcommerceHeader from '@/components/ecommerce/EcommerceHeader';
import EcommerceFooter from '@/components/ecommerce/EcommerceFooter';
import CartSidebar from '@/components/ecommerce/CartSidebar';
import MobileBottomBar from '@/components/ecommerce/MobileBottomBar';
import { Toaster } from '@/components/ui/toaster';

/**
 * Props pour le composant EcommerceLayout
 */
interface EcommerceLayoutProps {
  children: ReactNode;
}

/**
 * Layout principal pour les pages e-commerce
 *
 * Inclut l'en-tête et le pied de page
 *
 * @param children - Le contenu de la page
 */
export default function EcommerceLayout({ children }: EcommerceLayoutProps) {
  return (
    <div className="flex min-h-screen flex-col">
      <EcommerceHeader />
      {/* Ajouter un espace pour compenser la hauteur du header fixe */}
      <div className="h-[110px] md:h-[120px]"></div>
      <main className="flex-1">
        {children}
      </main>
      <EcommerceFooter />
      <CartSidebar />
      <MobileBottomBar />
      <Toaster />
    </div>
  );
}
