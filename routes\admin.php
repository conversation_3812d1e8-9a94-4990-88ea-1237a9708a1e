<?php

use App\Http\Controllers\Admin\ImportController;
use Illuminate\Support\Facades\Route;

// Routes d'administration protégées par le middleware auth et admin
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Routes d'importation
    Route::get('/import', [ImportController::class, 'index'])->name('import.index');
    Route::post('/import/categories', [ImportController::class, 'importCategories'])->name('import.categories');
    Route::get('/import/categories/preview', [ImportController::class, 'previewCategories'])->name('import.preview-categories');
    Route::post('/import/categories/confirm', [ImportController::class, 'confirmImportCategories'])->name('import.confirm-categories');
    Route::post('/import/products', [ImportController::class, 'importProducts'])->name('import.products');
    Route::get('/import/categories/template', [ImportController::class, 'downloadCategoriesTemplate'])->name('import.categories.template');
    Route::get('/import/products/template', [ImportController::class, 'downloadProductsTemplate'])->name('import.products.template');
});
