<?php

namespace App\Filament\Marchand\Resources;

use App\Filament\Marchand\Resources\PaiementResource\Pages;
use App\Models\Paiement;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class PaiementResource extends Resource
{
    protected static ?string $model = Paiement::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';
    
    protected static ?string $navigationGroup = 'Gestion des Commandes';
    
    protected static ?int $navigationSort = 3;
    
    protected static ?string $recordTitleAttribute = 'id';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Informations du paiement')
                            ->schema([
                                Forms\Components\TextInput::make('id')
                                    ->label('ID du paiement')
                                    ->disabled(),
                                
                                Forms\Components\TextInput::make('montant')
                                    ->label('Montant')
                                    ->disabled()
                                    ->prefix('€'),
                                
                                Forms\Components\Select::make('statut')
                                    ->label('Statut')
                                    ->options([
                                        'EnAttente' => 'En attente',
                                        'Complété' => 'Complété',
                                        'Échoué' => 'Échoué',
                                        'Remboursé' => 'Remboursé',
                                    ])
                                    ->disabled(),
                                
                                Forms\Components\TextInput::make('transaction_id')
                                    ->label('ID de transaction')
                                    ->disabled(),
                                
                                Forms\Components\TextInput::make('methode')
                                    ->label('Méthode de paiement')
                                    ->disabled(),
                                
                                Forms\Components\DateTimePicker::make('creeLe')
                                    ->label('Date du paiement')
                                    ->disabled(),
                            ]),
                    ])
                    ->columnSpan(['lg' => 2]),
                
                Forms\Components\Group::make()
                    ->schema([
                        Forms\Components\Section::make('Commande associée')
                            ->schema([
                                Forms\Components\TextInput::make('commande.id')
                                    ->label('ID de la commande')
                                    ->disabled(),
                                
                                Forms\Components\TextInput::make('commande.montantTotal')
                                    ->label('Montant de la commande')
                                    ->disabled()
                                    ->prefix('€'),
                                
                                Forms\Components\TextInput::make('commande.statut')
                                    ->label('Statut de la commande')
                                    ->disabled(),
                            ]),
                    ])
                    ->columnSpan(['lg' => 1]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->searchable(),
                
                Tables\Columns\TextColumn::make('commande.id')
                    ->label('Commande')
                    ->searchable(),
                
                Tables\Columns\TextColumn::make('montant')
                    ->label('Montant')
                    ->money('EUR')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('statut')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'EnAttente' => 'warning',
                        'Complété' => 'success',
                        'Échoué' => 'danger',
                        'Remboursé' => 'info',
                        default => 'gray',
                    }),
                
                Tables\Columns\TextColumn::make('methode')
                    ->label('Méthode'),
                
                Tables\Columns\TextColumn::make('creeLe')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('statut')
                    ->options([
                        'EnAttente' => 'En attente',
                        'Complété' => 'Complété',
                        'Échoué' => 'Échoué',
                        'Remboursé' => 'Remboursé',
                    ]),
                
                Tables\Filters\Filter::make('creeLe')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Depuis le'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Jusqu\'au'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('creeLe', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('creeLe', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPaiements::route('/'),
            'view' => Pages\ViewPaiement::route('/{record}'),
        ];
    }
    
    public static function getEloquentQuery(): Builder
    {
        // Filtrer les paiements pour n'afficher que ceux du marchand connecté
        return parent::getEloquentQuery()
            ->where('marchand_id', auth()->user()->marchands->first()->id ?? null);
    }
}
