<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DomainMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $allowedDomain = null): Response
    {
        $currentDomain = $request->getHost();
        $currentPort = $request->getPort();
        
        // Ajouter le port si ce n'est pas 80 ou 443
        if (!in_array($currentPort, [80, 443])) {
            $currentDomain .= ':' . $currentPort;
        }

        // En développement, utiliser les domaines de développement
        if (app()->environment('local')) {
            $domains = config('domains.development');
        } else {
            $domains = [
                'main' => config('domains.main.domain'),
                'admin' => config('domains.admin.domain'),
                'seller' => config('domains.seller.domain'),
                'cdn' => config('domains.cdn.domain'),
                'api' => config('domains.api.domain'),
            ];
        }

        // Si un domaine spécifique est requis
        if ($allowedDomain && isset($domains[$allowedDomain])) {
            $expectedDomain = $domains[$allowedDomain];
            
            if ($currentDomain !== $expectedDomain) {
                // Rediriger vers le bon domaine
                $url = $request->secure() ? 'https://' : 'http://';
                $url .= $expectedDomain . $request->getRequestUri();
                
                return redirect($url, 301);
            }
        }

        // Vérifier que le domaine est autorisé
        if (!in_array($currentDomain, array_values($domains))) {
            // Rediriger vers le domaine principal si le domaine n'est pas reconnu
            $mainDomain = $domains['main'];
            $url = $request->secure() ? 'https://' : 'http://';
            $url .= $mainDomain;
            
            return redirect($url, 301);
        }

        // Ajouter des informations sur le domaine à la requête
        $request->merge([
            'current_domain' => $currentDomain,
            'domain_type' => $this->getDomainType($currentDomain, $domains),
        ]);

        return $next($request);
    }

    /**
     * Déterminer le type de domaine
     */
    private function getDomainType(string $currentDomain, array $domains): string
    {
        foreach ($domains as $type => $domain) {
            if ($currentDomain === $domain) {
                return $type;
            }
        }
        
        return 'unknown';
    }
}
