<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Client;
use App\Models\Marchand;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Création du super admin (depuis les variables d'environnement)
        $adminEmail = env('email', '<EMAIL>');
        $superAdmin = User::firstOrCreate(
            ['email' => $adminEmail],
            [
                'name' => 'Franck Elysee',
                'password' => Hash::make(env('pass', '4321Franck')),
                'role' => 'Admin', // Super admin
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

        // Création de 3 marchands
        for ($i = 1; $i <= 3; $i++) {
            $email = "marchand{$i}@example.com";
            $user = User::firstOrCreate(
                ['email' => $email],
                [
                    'name' => "Marchand {$i}",
                    'password' => Hash::make('password'),
                    'role' => 'Marchand',
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );

            Marchand::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'nomEntreprise' => "Entreprise Marchand {$i}",
                    'idFiscal' => "FISC{$i}" . Str::random(8),
                    'banqueNom' => "Banque {$i}",
                    'banqueNumeroCompte' => "FR76" . rand(10000000000, 99999999999),
                ]
            );
        }

        // Création de 2 clients
        for ($i = 1; $i <= 2; $i++) {
            $email = "client{$i}@example.com";
            $prenom = "Prénom{$i}";
            $nom = "Nom{$i}";
            $user = User::firstOrCreate(
                ['email' => $email],
                [
                    'name' => "{$prenom} {$nom}",
                    'password' => Hash::make('password'),
                    'role' => 'Client',
                    'is_active' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            );

            Client::firstOrCreate(
                ['user_id' => $user->id],
                [
                    'prenom' => $prenom,
                    'nom' => $nom,
                    'telephone' => "+336" . rand(10000000, 99999999),
                ]
            );
        }
    }
}
