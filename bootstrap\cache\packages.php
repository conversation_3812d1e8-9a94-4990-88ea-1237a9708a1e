<?php return array (
  'anourvalar/eloquent-serialize' => 
  array (
    'aliases' => 
    array (
      'EloquentSerialize' => 'AnourValar\\EloquentSerialize\\Facades\\EloquentSerializeFacade',
    ),
  ),
  'blade-ui-kit/blade-heroicons' => 
  array (
    'providers' => 
    array (
      0 => 'BladeUI\\Heroicons\\BladeHeroiconsServiceProvider',
    ),
  ),
  'blade-ui-kit/blade-icons' => 
  array (
    'providers' => 
    array (
      0 => 'BladeUI\\Icons\\BladeIconsServiceProvider',
    ),
  ),
  'filament/actions' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Actions\\ActionsServiceProvider',
    ),
  ),
  'filament/filament' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\FilamentServiceProvider',
    ),
  ),
  'filament/forms' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Forms\\FormsServiceProvider',
    ),
  ),
  'filament/infolists' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Infolists\\InfolistsServiceProvider',
    ),
  ),
  'filament/notifications' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Notifications\\NotificationsServiceProvider',
    ),
  ),
  'filament/support' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Support\\SupportServiceProvider',
    ),
  ),
  'filament/tables' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Tables\\TablesServiceProvider',
    ),
  ),
  'filament/widgets' => 
  array (
    'providers' => 
    array (
      0 => 'Filament\\Widgets\\WidgetsServiceProvider',
    ),
  ),
  'inertiajs/inertia-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Inertia\\ServiceProvider',
    ),
  ),
  'jasonmccreary/laravel-test-assertions' => 
  array (
    'providers' => 
    array (
      0 => 'JMac\\Testing\\AdditionalAssertionsServiceProvider',
    ),
  ),
  'kirschbaum-development/eloquent-power-joins' => 
  array (
    'providers' => 
    array (
      0 => 'Kirschbaum\\PowerJoins\\PowerJoinsServiceProvider',
    ),
  ),
  'laravel-lang/actions' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Actions\\ServiceProvider',
    ),
  ),
  'laravel-lang/attributes' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Attributes\\ServiceProvider',
    ),
  ),
  'laravel-lang/config' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Config\\ServiceProvider',
    ),
  ),
  'laravel-lang/http-statuses' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\HttpStatuses\\ServiceProvider',
    ),
  ),
  'laravel-lang/lang' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Lang\\ServiceProvider',
    ),
  ),
  'laravel-lang/locales' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Locales\\ServiceProvider',
    ),
  ),
  'laravel-lang/models' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Models\\ServiceProvider',
    ),
  ),
  'laravel-lang/moonshine' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\MoonShine\\ServiceProvider',
    ),
  ),
  'laravel-lang/publisher' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Publisher\\ServiceProvider',
    ),
  ),
  'laravel-lang/routes' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\Routes\\ServiceProvider',
    ),
  ),
  'laravel-lang/starter-kits' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelLang\\StarterKits\\ServiceProvider',
    ),
  ),
  'laravel-shift/blueprint' => 
  array (
    'providers' => 
    array (
      0 => 'Blueprint\\BlueprintServiceProvider',
    ),
  ),
  'laravel/pail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Pail\\PailServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'livewire/livewire' => 
  array (
    'aliases' => 
    array (
      'Livewire' => 'Livewire\\Livewire',
    ),
    'providers' => 
    array (
      0 => 'Livewire\\LivewireServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'pestphp/pest-plugin-laravel' => 
  array (
    'providers' => 
    array (
      0 => 'Pest\\Laravel\\PestServiceProvider',
    ),
  ),
  'ryangjchandler/blade-capture-directive' => 
  array (
    'aliases' => 
    array (
      'BladeCaptureDirective' => 'RyanChandler\\BladeCaptureDirective\\Facades\\BladeCaptureDirective',
    ),
    'providers' => 
    array (
      0 => 'RyanChandler\\BladeCaptureDirective\\BladeCaptureDirectiveServiceProvider',
    ),
  ),
  'spatie/laravel-translatable' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Translatable\\TranslatableServiceProvider',
    ),
  ),
  'tightenco/ziggy' => 
  array (
    'providers' => 
    array (
      0 => 'Tighten\\Ziggy\\ZiggyServiceProvider',
    ),
  ),
);