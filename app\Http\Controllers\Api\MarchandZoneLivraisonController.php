<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MarchandZoneLivraison;
use App\Models\User;
use App\Models\ZoneLivraison;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class MarchandZoneLivraisonController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $query = MarchandZoneLivraison::query();

        // Si l'utilisateur est un marchand, filtrer par son ID
        if (Auth::user() && Auth::user()->role === 'Marchand') {
            $query->where('marchand_id', Auth::id());
        }
        // Sinon, si un ID de marchand est fourni, filtrer par cet ID
        elseif ($request->has('marchand_id')) {
            $query->where('marchand_id', $request->marchand_id);
        }

        // Filtrer par zone de livraison
        if ($request->has('zone_livraison_id')) {
            $query->where('zone_livraison_id', $request->zone_livraison_id);
        }

        // Filtrer par actif
        if ($request->has('actif')) {
            $query->where('actif', $request->actif);
        }

        // Inclure les relations
        if ($request->has('with')) {
            $relations = explode(',', $request->with);
            $allowedRelations = ['marchand', 'zoneLivraison'];
            $validRelations = array_intersect($relations, $allowedRelations);

            if (!empty($validRelations)) {
                $query->with($validRelations);
            }
        }

        // Pagination
        $perPage = $request->input('per_page', 15);
        $marchandZones = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $marchandZones,
            'message' => 'Zones de livraison du marchand récupérées avec succès'
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'marchand_id' => 'required|exists:users,id',
            'zone_livraison_id' => 'required|exists:zones_livraison,id',
            'frais_livraison' => 'required|numeric|min:0',
            'delai_livraison_min' => 'required|integer|min:0',
            'delai_livraison_max' => 'required|integer|gte:delai_livraison_min',
            'actif' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
                'message' => 'Validation échouée'
            ], 422);
        }

        // Vérifier si l'utilisateur est un marchand
        $user = User::find($request->marchand_id);
        if (!$user || $user->role !== 'Marchand') {
            return response()->json([
                'success' => false,
                'message' => 'L\'utilisateur spécifié n\'est pas un marchand'
            ], 400);
        }

        // Vérifier si la zone de livraison existe et est active
        $zone = ZoneLivraison::find($request->zone_livraison_id);
        if (!$zone || !$zone->actif) {
            return response()->json([
                'success' => false,
                'message' => 'La zone de livraison spécifiée n\'existe pas ou n\'est pas active'
            ], 400);
        }

        // Vérifier si l'association existe déjà
        $existingAssociation = MarchandZoneLivraison::where('marchand_id', $request->marchand_id)
            ->where('zone_livraison_id', $request->zone_livraison_id)
            ->first();

        if ($existingAssociation) {
            return response()->json([
                'success' => false,
                'message' => 'Cette association marchand-zone existe déjà'
            ], 400);
        }

        $marchandZone = MarchandZoneLivraison::create($request->all());

        return response()->json([
            'success' => true,
            'data' => $marchandZone,
            'message' => 'Association marchand-zone créée avec succès'
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        $marchandZone = MarchandZoneLivraison::with(['marchand', 'zoneLivraison'])->find($id);

        if (!$marchandZone) {
            return response()->json([
                'success' => false,
                'message' => 'Association marchand-zone non trouvée'
            ], 404);
        }

        // Si l'utilisateur est un marchand, vérifier qu'il est bien le propriétaire
        if (Auth::user() && Auth::user()->role === 'Marchand' && $marchandZone->marchand_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Vous n\'êtes pas autorisé à accéder à cette ressource'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $marchandZone,
            'message' => 'Association marchand-zone récupérée avec succès'
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $marchandZone = MarchandZoneLivraison::find($id);

        if (!$marchandZone) {
            return response()->json([
                'success' => false,
                'message' => 'Association marchand-zone non trouvée'
            ], 404);
        }

        // Si l'utilisateur est un marchand, vérifier qu'il est bien le propriétaire
        if (Auth::user() && Auth::user()->role === 'Marchand' && $marchandZone->marchand_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Vous n\'êtes pas autorisé à modifier cette ressource'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'frais_livraison' => 'sometimes|required|numeric|min:0',
            'delai_livraison_min' => 'sometimes|required|integer|min:0',
            'delai_livraison_max' => 'sometimes|required|integer|gte:delai_livraison_min',
            'actif' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
                'message' => 'Validation échouée'
            ], 422);
        }

        $marchandZone->update($request->all());

        return response()->json([
            'success' => true,
            'data' => $marchandZone,
            'message' => 'Association marchand-zone mise à jour avec succès'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param string $id
     * @return JsonResponse
     */
    public function destroy(string $id): JsonResponse
    {
        $marchandZone = MarchandZoneLivraison::find($id);

        if (!$marchandZone) {
            return response()->json([
                'success' => false,
                'message' => 'Association marchand-zone non trouvée'
            ], 404);
        }

        // Si l'utilisateur est un marchand, vérifier qu'il est bien le propriétaire
        if (Auth::user() && Auth::user()->role === 'Marchand' && $marchandZone->marchand_id !== Auth::id()) {
            return response()->json([
                'success' => false,
                'message' => 'Vous n\'êtes pas autorisé à supprimer cette ressource'
            ], 403);
        }

        // Vérifier si l'association est utilisée par des produits
        if ($marchandZone->produitZonesLivraison()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Impossible de supprimer cette association car elle est utilisée par des produits'
            ], 400);
        }

        $marchandZone->delete();

        return response()->json([
            'success' => true,
            'message' => 'Association marchand-zone supprimée avec succès'
        ]);
    }

    /**
     * Récupérer les zones de livraison d'un marchand spécifique.
     *
     * @param string $marchandId
     * @return JsonResponse
     */
    public function getZonesByMarchand(string $marchandId): JsonResponse
    {
        // Vérifier si le marchand existe
        $marchand = User::find($marchandId);
        if (!$marchand || $marchand->role !== 'Marchand') {
            return response()->json([
                'success' => false,
                'message' => 'Marchand non trouvé'
            ], 404);
        }

        // Récupérer les zones de livraison du marchand
        $zones = MarchandZoneLivraison::where('marchand_id', $marchandId)
            ->where('actif', true)
            ->with('zoneLivraison')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $zones,
            'message' => 'Zones de livraison du marchand récupérées avec succès'
        ]);
    }

    /**
     * Récupérer les marchands qui livrent dans une zone spécifique.
     *
     * @param string $zoneId
     * @return JsonResponse
     */
    public function getMarchandsByZone(string $zoneId): JsonResponse
    {
        // Vérifier si la zone existe
        $zone = ZoneLivraison::find($zoneId);
        if (!$zone) {
            return response()->json([
                'success' => false,
                'message' => 'Zone de livraison non trouvée'
            ], 404);
        }

        // Récupérer les marchands qui livrent dans cette zone
        $marchands = MarchandZoneLivraison::where('zone_livraison_id', $zoneId)
            ->where('actif', true)
            ->with('marchand')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $marchands,
            'message' => 'Marchands livrant dans cette zone récupérés avec succès'
        ]);
    }
}
