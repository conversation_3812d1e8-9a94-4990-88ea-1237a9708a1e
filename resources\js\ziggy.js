const Ziggy = {"url":"http:\/\/localhost:8000","port":8000,"defaults":{},"routes":{"filament.exports.download":{"uri":"filament\/exports\/{export}\/download","methods":["GET","HEAD"],"parameters":["export"],"bindings":{"export":"id"}},"filament.imports.failed-rows.download":{"uri":"filament\/imports\/{import}\/failed-rows\/download","methods":["GET","HEAD"],"parameters":["import"],"bindings":{"import":"id"}},"filament.admin.auth.login":{"uri":"admin\/login","methods":["GET","HEAD"]},"filament.admin.auth.logout":{"uri":"admin\/logout","methods":["POST"]},"filament.admin.pages.dashboard":{"uri":"admin","methods":["GET","HEAD"]},"filament.admin.resources.banners.index":{"uri":"admin\/banners","methods":["GET","HEAD"]},"filament.admin.resources.banners.create":{"uri":"admin\/banners\/create","methods":["GET","HEAD"]},"filament.admin.resources.banners.edit":{"uri":"admin\/banners\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.categories.index":{"uri":"admin\/categories","methods":["GET","HEAD"]},"filament.admin.resources.categories.create":{"uri":"admin\/categories\/create","methods":["GET","HEAD"]},"filament.admin.resources.categories.edit":{"uri":"admin\/categories\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.commandes.index":{"uri":"admin\/commandes","methods":["GET","HEAD"]},"filament.admin.resources.commandes.create":{"uri":"admin\/commandes\/create","methods":["GET","HEAD"]},"filament.admin.resources.commandes.edit":{"uri":"admin\/commandes\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.coupons.index":{"uri":"admin\/coupons","methods":["GET","HEAD"]},"filament.admin.resources.coupons.create":{"uri":"admin\/coupons\/create","methods":["GET","HEAD"]},"filament.admin.resources.coupons.edit":{"uri":"admin\/coupons\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.paiements.index":{"uri":"admin\/paiements","methods":["GET","HEAD"]},"filament.admin.resources.paiements.create":{"uri":"admin\/paiements\/create","methods":["GET","HEAD"]},"filament.admin.resources.paiements.edit":{"uri":"admin\/paiements\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.produits.index":{"uri":"admin\/produits","methods":["GET","HEAD"]},"filament.admin.resources.produits.create":{"uri":"admin\/produits\/create","methods":["GET","HEAD"]},"filament.admin.resources.produits.edit":{"uri":"admin\/produits\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.users.index":{"uri":"admin\/users","methods":["GET","HEAD"]},"filament.admin.resources.users.create":{"uri":"admin\/users\/create","methods":["GET","HEAD"]},"filament.admin.resources.users.view":{"uri":"admin\/users\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.admin.resources.users.edit":{"uri":"admin\/users\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.marchand.auth.login":{"uri":"marchand\/login","methods":["GET","HEAD"]},"filament.marchand.auth.logout":{"uri":"marchand\/logout","methods":["POST"]},"filament.marchand.pages.dashboard":{"uri":"marchand","methods":["GET","HEAD"]},"filament.marchand.resources.categories.index":{"uri":"marchand\/categories","methods":["GET","HEAD"]},"filament.marchand.resources.categories.view":{"uri":"marchand\/categories\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.marchand.resources.commandes.index":{"uri":"marchand\/commandes","methods":["GET","HEAD"]},"filament.marchand.resources.commandes.create":{"uri":"marchand\/commandes\/create","methods":["GET","HEAD"]},"filament.marchand.resources.commandes.edit":{"uri":"marchand\/commandes\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.marchand.resources.paiements.index":{"uri":"marchand\/paiements","methods":["GET","HEAD"]},"filament.marchand.resources.paiements.view":{"uri":"marchand\/paiements\/{record}","methods":["GET","HEAD"],"parameters":["record"]},"filament.marchand.resources.produits.index":{"uri":"marchand\/produits","methods":["GET","HEAD"]},"filament.marchand.resources.produits.create":{"uri":"marchand\/produits\/create","methods":["GET","HEAD"]},"filament.marchand.resources.produits.edit":{"uri":"marchand\/produits\/{record}\/edit","methods":["GET","HEAD"],"parameters":["record"]},"filament.marchand.resources.mon-profil.index":{"uri":"marchand\/mon-profil","methods":["GET","HEAD"]},"livewire.update":{"uri":"livewire\/update","methods":["POST"]},"livewire.upload-file":{"uri":"livewire\/upload-file","methods":["POST"]},"livewire.preview-file":{"uri":"livewire\/preview-file\/{filename}","methods":["GET","HEAD"],"parameters":["filename"]},"welcome":{"uri":"welcome","methods":["GET","HEAD"]},"dashboard":{"uri":"dashboard","methods":["GET","HEAD"]},"profile.edit":{"uri":"settings\/profile","methods":["GET","HEAD"]},"profile.update":{"uri":"settings\/profile","methods":["PATCH"]},"profile.destroy":{"uri":"settings\/profile","methods":["DELETE"]},"password.edit":{"uri":"settings\/password","methods":["GET","HEAD"]},"password.update":{"uri":"settings\/password","methods":["PUT"]},"appearance":{"uri":"settings\/appearance","methods":["GET","HEAD"]},"register":{"uri":"register","methods":["GET","HEAD"]},"login":{"uri":"login","methods":["GET","HEAD"]},"password.request":{"uri":"forgot-password","methods":["GET","HEAD"]},"password.email":{"uri":"forgot-password","methods":["POST"]},"password.reset":{"uri":"reset-password\/{token}","methods":["GET","HEAD"],"parameters":["token"]},"password.store":{"uri":"reset-password","methods":["POST"]},"verification.notice":{"uri":"verify-email","methods":["GET","HEAD"]},"verification.verify":{"uri":"verify-email\/{id}\/{hash}","methods":["GET","HEAD"],"parameters":["id","hash"]},"verification.send":{"uri":"email\/verification-notification","methods":["POST"]},"password.confirm":{"uri":"confirm-password","methods":["GET","HEAD"]},"logout":{"uri":"logout","methods":["POST"]},"home":{"uri":"\/","methods":["GET","HEAD"]},"categories":{"uri":"categories","methods":["GET","HEAD"]},"category":{"uri":"categories\/{categorySlug}","methods":["GET","HEAD"],"parameters":["categorySlug"]},"products":{"uri":"products","methods":["GET","HEAD"]},"product":{"uri":"products\/{productSlug}","methods":["GET","HEAD"],"parameters":["productSlug"]},"cart":{"uri":"cart","methods":["GET","HEAD"]},"search":{"uri":"search","methods":["GET","HEAD"]},"my-account":{"uri":"my-account","methods":["GET","HEAD"]},"my-account.personal-info.update":{"uri":"my-account\/personal-info","methods":["POST"]},"my-account.addresses.add":{"uri":"my-account\/addresses","methods":["POST"]},"my-account.addresses.update":{"uri":"my-account\/addresses\/{id}","methods":["PUT"],"parameters":["id"]},"my-account.addresses.delete":{"uri":"my-account\/addresses\/{id}","methods":["DELETE"],"parameters":["id"]},"my-account.addresses.default":{"uri":"my-account\/addresses\/{id}\/default","methods":["POST"],"parameters":["id"]},"storage.local":{"uri":"storage\/{path}","methods":["GET","HEAD"],"wheres":{"path":".*"},"parameters":["path"]}}};
if (typeof window !== 'undefined' && typeof window.Ziggy !== 'undefined') {
  Object.assign(Ziggy.routes, window.Ziggy.routes);
}
export { Ziggy };
