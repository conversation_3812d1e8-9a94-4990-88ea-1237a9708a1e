import { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import { useCart } from '@/contexts/CartContext';
import { useDelivery } from '@/contexts/DeliveryContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Gift, Loader2, Minus, Plus, ShoppingCart, Trash2, MapPin } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useTranslation } from '@/hooks/use-translation';
import ZoneLivraisonSelector from '@/components/ecommerce/ZoneLivraisonSelector';

/**
 * Page du panier d'achat
 */
export default function CartPage() {
    const {
        items,
        removeItem,
        updateQuantity,
        subtotal,
        formattedSubtotal,
        deliveryFees,
        formattedDeliveryFees,
        grandTotal,
        formattedGrandTotal,
        itemCount,
        estimatedDeliveryTime
    } = useCart();
    const { selectedZone } = useDelivery();
    const [couponCode, setCouponCode] = useState('');
    const [isApplyingCoupon, setIsApplyingCoupon] = useState(false);
    const { translate } = useTranslation();

    /**
     * Génère une clé unique pour un produit basée sur ses attributs
     *
     * @param product - Le produit pour lequel générer une clé
     * @returns Une chaîne représentant la combinaison unique d'attributs
     */
    const getProductVariantKey = (product: any): string => {
        // Extraire les attributs de couleur, taille et matière
        const colorAttr = product.attributes.find((a: any) => a.type === 'couleur');
        const sizeAttr = product.attributes.find((a: any) => a.type === 'taille');
        const materialAttr = product.attributes.find((a: any) => a.type === 'matiere');

        // Créer une chaîne qui représente la combinaison unique d'attributs
        let variantKey = product.id;

        if (colorAttr && 'nom' in colorAttr) variantKey += `-color:${colorAttr.nom}`;
        if (sizeAttr && 'valeur' in sizeAttr) variantKey += `-size:${sizeAttr.valeur}`;
        if (materialAttr && 'valeur' in materialAttr) variantKey += `-material:${materialAttr.valeur}`;

        return variantKey;
    };

    /**
     * Augmente la quantité d'un élément du panier
     *
     * @param product - Le produit
     * @param currentQuantity - La quantité actuelle
     */
    const handleIncreaseQuantity = (product: any, currentQuantity: number) => {
        const variantKey = getProductVariantKey(product);
        updateQuantity(product.id, currentQuantity + 1, variantKey);
    };

    /**
     * Diminue la quantité d'un élément du panier
     *
     * @param product - Le produit
     * @param currentQuantity - La quantité actuelle
     */
    const handleDecreaseQuantity = (product: any, currentQuantity: number) => {
        if (currentQuantity > 1) {
            const variantKey = getProductVariantKey(product);
            updateQuantity(product.id, currentQuantity - 1, variantKey);
        }
    };

    /**
     * Applique un code promo
     *
     * @param e - L'événement de soumission
     */
    const handleApplyCoupon = (e: React.FormEvent) => {
        e.preventDefault();
        if (couponCode.trim()) {
            setIsApplyingCoupon(true);

            // Simulation d'un appel API
            setTimeout(() => {
                setIsApplyingCoupon(false);
                setCouponCode('');
                // Dans une implémentation réelle, on mettrait à jour le total avec la réduction
            }, 1000);
        }
    };

    /**
     * Vérifie si tous les produits du panier proviennent du même marchand
     *
     * @returns {boolean} true si tous les produits ont le même marchand, false sinon
     */
    const allProductsFromSameMerchant = (): boolean => {
        if (items.length <= 1) return true;

        const firstSeller = items[0].product.seller;
        return items.every(item => item.product.seller === firstSeller);
    };

    /**
     * Calcule les frais de livraison totaux par marchand
     *
     * @returns {Object} Un objet avec les frais de livraison par marchand
     */
    const getDeliveryFeesByMerchant = () => {
        const feesByMerchant: Record<string, {
            fees: number,
            merchantName: string,
            zoneName: string | undefined
        }> = {};

        items.forEach(item => {
            if (item.deliveryInfo) {
                const sellerId = item.product.seller || 'unknown';

                if (!feesByMerchant[sellerId]) {
                    feesByMerchant[sellerId] = {
                        fees: 0,
                        merchantName: item.product.seller || translate('common.unknown_merchant'),
                        zoneName: item.deliveryInfo.zone_nom
                    };
                }

                feesByMerchant[sellerId].fees += item.getDeliveryFees();
            }
        });

        return feesByMerchant;
    };

    // Vérifier si tous les produits viennent du même marchand
    const sameMerchant = allProductsFromSameMerchant();

    // Obtenir les frais de livraison par marchand
    const deliveryFeesByMerchant = getDeliveryFeesByMerchant();

    return (
        <EcommerceLayout>
            <Head title={translate('pages.cart.title')} />

            <div className="container mx-auto px-4 py-8">
                <h1 className="mb-6 text-2xl font-bold sm:text-3xl">{translate('pages.cart.header')}</h1>

                {items.length > 0 ? (
                    <div className="grid gap-8 lg:grid-cols-3">
                        {/* Liste des produits */}
                        <div className="lg:col-span-2">
                            <div className="rounded-lg border">
                                <div className="p-6">
                                    <h2 className="mb-4 text-lg font-medium">
                                        {translate('pages.cart.items_count', { count: itemCount })}
                                    </h2>

                                    <div className="space-y-6">
                                        {items.map((item) => (
                                            <div key={item.product.id} className="flex flex-col gap-4 sm:flex-row">
                                                <div className="h-24 w-24 flex-shrink-0 overflow-hidden rounded-md border">
                                                    <img
                                                        src={item.product.imageUrl}
                                                        alt={item.product.name}
                                                        className="h-full w-full object-cover"
                                                    />
                                                </div>

                                                <div className="flex flex-1 flex-col justify-between">
                                                    <div className="flex justify-between">
                                                        <div>
                                                            <h3 className="text-base font-medium">
                                                                <Link
                                                                    href={route('product', { productSlug: item.product.slug })}
                                                                    className="hover:text-primary hover:underline"
                                                                >
                                                                    {item.product.name}
                                                                </Link>
                                                            </h3>

                                                            {/* Affichage des attributs sélectionnés */}
                                                            <div className="mt-1 flex flex-wrap gap-1">
                                                                {item.product.attributes
                                                                    .filter(attr =>
                                                                        (attr.type === 'couleur' && 'nom' in attr) ||
                                                                        (attr.type === 'taille' && 'valeur' in attr) ||
                                                                        (attr.type === 'matiere' && 'valeur' in attr)
                                                                    )
                                                                    .map((attr, index) => {
                                                                        let label = '';
                                                                        let value = '';

                                                                        if (attr.type === 'couleur' && 'nom' in attr) {
                                                                            label = translate('common.color');
                                                                            value = attr.nom;

                                                                            // Pour les attributs de couleur, on affiche un indicateur visuel
                                                                            return (
                                                                                <span
                                                                                    key={index}
                                                                                    className="inline-flex items-center rounded-md bg-primary/10 px-2 py-1 text-xs font-medium text-primary"
                                                                                >
                                                                                    {/* Afficher l'image de la couleur ou un carré de couleur */}
                                                                                    {'with_image' in attr && attr.with_image && 'color_image' in attr && attr.color_image ? (
                                                                                        <img
                                                                                            src={attr.color_image}
                                                                                            alt={value}
                                                                                            className="mr-1 h-4 w-4 rounded-full object-cover"
                                                                                        />
                                                                                    ) : (
                                                                                        <span
                                                                                            className="mr-1 inline-block h-4 w-4 rounded-full border"
                                                                                            style={{ backgroundColor: 'code' in attr ? attr.code : '#CCCCCC' }}
                                                                                        ></span>
                                                                                    )}
                                                                                    {label}: {value}
                                                                                </span>
                                                                            );
                                                                        } else if (attr.type === 'taille' && 'valeur' in attr) {
                                                                            label = translate('common.size');
                                                                            value = attr.valeur;
                                                                        } else if (attr.type === 'matiere' && 'valeur' in attr) {
                                                                            label = translate('common.material');
                                                                            value = attr.valeur;
                                                                        }

                                                                        if (!label || !value) return null;

                                                                        // Pour les autres types d'attributs (taille, matière, etc.)
                                                                        return (
                                                                            <span
                                                                                key={index}
                                                                                className="inline-flex items-center rounded-md bg-primary/10 px-2 py-1 text-xs font-medium text-primary"
                                                                            >
                                                                                {label}: {value}
                                                                            </span>
                                                                        );
                                                                    })
                                                                }
                                                            </div>

                                                            <p className="mt-1 text-sm text-muted-foreground">
                                                                {item.product.formattedPrice()}
                                                            </p>
                                                        </div>
                                                        <p className="text-right font-medium">
                                                            {item.formattedSubtotal()}
                                                        </p>
                                                    </div>

                                                    <div className="mt-2 flex items-center justify-between">
                                                        <div className="flex items-center rounded-md border">
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => handleDecreaseQuantity(item.product, item.quantity)}
                                                                disabled={item.quantity <= 1}
                                                                className="h-8 w-8 rounded-none"
                                                            >
                                                                <Minus className="h-3 w-3" />
                                                            </Button>
                                                            <span className="w-8 text-center text-sm">{item.quantity}</span>
                                                            <Button
                                                                variant="ghost"
                                                                size="icon"
                                                                onClick={() => handleIncreaseQuantity(item.product, item.quantity)}
                                                                className="h-8 w-8 rounded-none"
                                                            >
                                                                <Plus className="h-3 w-3" />
                                                            </Button>
                                                        </div>

                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => removeItem(item.product.id, getProductVariantKey(item.product))}
                                                            className="text-muted-foreground hover:text-destructive"
                                                        >
                                                            <Trash2 className="mr-1 h-4 w-4" />
                                                            {translate('pages.cart.remove')}
                                                        </Button>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </div>

                            {/* Continuer les achats */}
                            <div className="mt-6">
                                <Button variant="outline" asChild>
                                    <Link href={route('home')}>
                                        <ArrowLeft className="mr-2 h-4 w-4" />
                                        {translate('pages.cart.continue_shopping')}
                                    </Link>
                                </Button>
                            </div>
                        </div>

                        {/* Récapitulatif */}
                        <div>
                            <div className="rounded-lg border">
                                <div className="p-6">
                                    <h2 className="mb-4 text-lg font-medium">
                                        {translate('pages.cart.order_summary')}
                                    </h2>

                                    <div className="space-y-4">
                                        {/* Sélecteur de zone de livraison */}
                                        <div className="mb-4">
                                            <div className="flex items-center mb-2">
                                                <MapPin className="h-4 w-4 mr-2" />
                                                <h3 className="text-sm font-medium">{translate('pages.cart.delivery_zone')}</h3>
                                            </div>
                                            <ZoneLivraisonSelector compact={selectedZone !== null} />
                                        </div>

                                        <Separator />

                                        <div className="flex justify-between">
                                            <span className="text-muted-foreground">{translate('pages.cart.subtotal')} ({itemCount} articles) </span>
                                            <span>{formattedSubtotal}</span>
                                        </div>

                                        {/* Frais de livraison */}
                                        {Object.keys(deliveryFeesByMerchant).length > 0 ? (
                                            <>
                                                {Object.entries(deliveryFeesByMerchant).map(([merchantId, info], index) => (
                                                    <div key={merchantId} className="space-y-1">
                                                        <div className="flex justify-between">
                                                            <span className="text-muted-foreground">
                                                                {sameMerchant
                                                                    ? translate('pages.cart.delivery_fees')
                                                                    : `${translate('pages.cart.delivery_fees')} (${info.merchantName})`}
                                                            </span>
                                                            <span>
                                                                {info.fees > 0
                                                                    ? `${info.fees.toLocaleString('fr-FR', { minimumFractionDigits: 0, maximumFractionDigits: 0 })} FCFA`
                                                                    : translate('pages.cart.calculate_at_next_step')}
                                                            </span>
                                                        </div>
                                                        {info.zoneName && (
                                                            <div className="flex justify-end">
                                                                <span className="text-xs text-muted-foreground">
                                                                    {translate('common.zone')}: {info.zoneName}
                                                                </span>
                                                            </div>
                                                        )}
                                                        {index < Object.entries(deliveryFeesByMerchant).length - 1 && (
                                                            <Separator className="my-1" />
                                                        )}
                                                    </div>
                                                ))}
                                            </>
                                        ) : (
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">{translate('pages.cart.delivery_fees')}</span>
                                                {selectedZone ? (
                                                    <span>{formattedDeliveryFees}</span>
                                                ) : (
                                                    <span className='text-sm'>{translate('pages.cart.calculate_at_next_step')}</span>
                                                )}
                                            </div>
                                        )}

                                        {selectedZone && estimatedDeliveryTime && (
                                            <div className="flex justify-between">
                                                <span className="text-muted-foreground">{translate('pages.cart.estimated_delivery')}</span>
                                                <span className='text-sm'>
                                                    {estimatedDeliveryTime.min === estimatedDeliveryTime.max
                                                        ? `${estimatedDeliveryTime.min} ${translate('common.days')}`
                                                        : `${estimatedDeliveryTime.min}-${estimatedDeliveryTime.max} ${translate('common.days')}`}
                                                </span>
                                            </div>
                                        )}

                                        <Separator />

                                        <div className="flex justify-between font-medium">
                                            <span> {translate('pages.cart.total')}</span>
                                            <span>{formattedGrandTotal}</span>
                                        </div>

                                        <Button className="w-full">
                                            <Link href={route('checkout')}>{translate('pages.cart.checkout')}</Link>
                                        </Button>

                                        <Alert variant="default" className="bg-muted/50">
                                            <AlertDescription className="text-xs">
                                                {translate('pages.cart.secure_payment_description')}
                                            </AlertDescription>
                                        </Alert>
                                    </div>

                                    {/* Code promo */}
                                    <div className="mt-6">
                                        <h3 className="mb-2 text-sm font-medium">{translate('pages.cart.apply_coupon')}</h3>
                                        <form onSubmit={handleApplyCoupon} className="flex gap-2">
                                            <Input
                                                type="text"
                                                value={couponCode}
                                                onChange={(e) => setCouponCode(e.target.value)}
                                                placeholder="Entrez votre code"
                                                className="flex-1"
                                            />
                                            <Button
                                                type="submit"
                                                variant="outline"
                                                disabled={!couponCode.trim() || isApplyingCoupon}
                                            >
                                                {isApplyingCoupon ? (
                                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                ) : (
                                                    <Gift className="mr-2 h-4 w-4" />
                                                )}
                                                {translate("pages.cart.apply_coupon_btn_text")}
                                            </Button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                ) : (
                    <div className="rounded-lg border border-dashed p-12 text-center">
                        <div className="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-muted">
                            <ShoppingCart className="h-10 w-10 text-muted-foreground" />
                        </div>
                        <h2 className="mb-2 text-xl font-medium">Votre panier est vide</h2>
                        <p className="mb-6 text-muted-foreground">
                            Vous n'avez pas encore ajouté de produits à votre panier.
                        </p>
                        <Button asChild>
                            <Link href={route('home')}>Commencer vos achats</Link>
                        </Button>
                    </div>
                )}
            </div>
        </EcommerceLayout>
    );
}
