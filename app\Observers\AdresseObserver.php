<?php

namespace App\Observers;

use App\Models\Adresse;

class AdresseObserver
{
    /**
     * Handle the Adresse "creating" event.
     */
    public function creating(Adresse $adresse): void
    {
        // S'assurer que toutes les données sont correctement formatées
        $adresse->rue = $adresse->rue === null ? '' : (string) $adresse->rue;
        $adresse->ville = $adresse->ville === null ? '' : (string) $adresse->ville;
        $adresse->etat = $adresse->etat === null ? '' : (string) $adresse->etat;
        $adresse->pays = $adresse->pays === null ? '' : (string) $adresse->pays;
        $adresse->codePostal = $adresse->codePostal === null ? '' : (string) $adresse->codePostal;
        $adresse->type = $adresse->type === null ? '' : (string) $adresse->type;
        $adresse->user_id = $adresse->user_id === null ? null : (int) $adresse->user_id;
    }

    /**
     * Handle the Adresse "created" event.
     */
    public function created(Adresse $adresse): void
    {
        //
    }

    /**
     * Handle the Adresse "updating" event.
     */
    public function updating(Adresse $adresse): void
    {
        // S'assurer que toutes les données sont correctement formatées
        $adresse->rue = $adresse->rue === null ? '' : (string) $adresse->rue;
        $adresse->ville = $adresse->ville === null ? '' : (string) $adresse->ville;
        $adresse->etat = $adresse->etat === null ? '' : (string) $adresse->etat;
        $adresse->pays = $adresse->pays === null ? '' : (string) $adresse->pays;
        $adresse->codePostal = $adresse->codePostal === null ? '' : (string) $adresse->codePostal;
        $adresse->type = $adresse->type === null ? '' : (string) $adresse->type;
        $adresse->user_id = $adresse->user_id === null ? null : (int) $adresse->user_id;
    }

    /**
     * Handle the Adresse "updated" event.
     */
    public function updated(Adresse $adresse): void
    {
        //
    }

    /**
     * Handle the Adresse "deleted" event.
     */
    public function deleted(Adresse $adresse): void
    {
        //
    }

    /**
     * Handle the Adresse "restored" event.
     */
    public function restored(Adresse $adresse): void
    {
        //
    }

    /**
     * Handle the Adresse "force deleted" event.
     */
    public function forceDeleted(Adresse $adresse): void
    {
        //
    }
}
