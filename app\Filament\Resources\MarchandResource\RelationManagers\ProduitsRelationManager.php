<?php

namespace App\Filament\Resources\MarchandResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProduitsRelationManager extends RelationManager
{
    protected static string $relationship = 'produits';

    protected static ?string $recordTitleAttribute = 'nom';

    protected static ?string $title = 'Produits';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('nom')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('nom')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('nom')
                    ->label('Nom')
                    ->formatStateUsing(function ($state) {
                        if (is_array($state)) {
                            return $state['fr'] ?? '';
                        }
                        return $state;
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('categorie.nom')
                    ->label('Catégorie')
                    ->formatStateUsing(function ($state) {
                        if (is_array($state)) {
                            return $state['fr'] ?? '';
                        }
                        return $state;
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('prix')
                    ->label('Prix')
                    ->money('EUR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('stock')
                    ->label('Stock')
                    ->sortable(),
                Tables\Columns\IconColumn::make('stock')
                    ->label('Disponibilité')
                    ->boolean()
                    ->getStateUsing(fn ($record) => $record->stock > 0)
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Créé le')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('categorie_id')
                    ->label('Catégorie')
                    ->relationship('categorie', 'nom', function ($query) {
                        return $query->orderBy('nom');
                    })
                    ->searchable()
                    ->preload(),
                Tables\Filters\Filter::make('en_stock')
                    ->label('En stock')
                    ->query(fn (Builder $query): Builder => $query->where('stock', '>', 0)),
                Tables\Filters\Filter::make('en_rupture')
                    ->label('En rupture')
                    ->query(fn (Builder $query): Builder => $query->where('stock', '=', 0)),
                Tables\Filters\Filter::make('en_promotion')
                    ->label('En promotion')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('discount_price')),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
