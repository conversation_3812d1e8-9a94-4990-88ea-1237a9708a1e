# Système de Livraison pour Lorrelei E-commerce

## Vue d'ensemble

Ce document décrit les étapes nécessaires pour implémenter un système de livraison géré par les marchands, similaire à celui d'Amazon. Dans ce système, chaque marchand définit:
- Les zones géographiques où il peut livrer ses produits
- Les tarifs de livraison pour chaque zone
- Les conditions spécifiques de livraison par produit

Le système filtrera automatiquement les produits affichés aux clients en fonction de leur localisation, ne montrant que les produits qui peuvent être livrés à leur adresse.

## Acteurs du système

1. **Marchand**:
   - Définit les zones de livraison
   - Fixe les tarifs de livraison
   - Associe les produits aux zones de livraison

2. **Client**:
   - Fournit son adresse de livraison
   - Voit uniquement les produits livrables à son adresse

3. **Administrateur**:
   - Supervise le système
   - Peut gérer les zones géographiques globales
   - Peut intervenir en cas de litige

## Étapes d'implémentation

### 1. Modélisation des données

#### 1.1 Création des tables de base de données

1. **Table `zones_livraison`**:
   ```
   - id (PK)
   - nom (string) - Ex: "Europe", "Afrique de l'Ouest", "Yaoundé Centre"
   - type (enum) - "Pays", "Région", "Ville", "Quartier"
   - parent_id (FK, nullable) - Référence à une zone parente
   - code (string, nullable) - Code postal, code pays, etc.
   - actif (boolean)
   - created_at, updated_at
   ```

2. **Table `marchand_zones_livraison`**:
   ```
   - id (PK)
   - marchand_id (FK)
   - zone_livraison_id (FK)
   - frais_livraison (decimal)
   - delai_livraison_min (integer) - En jours
   - delai_livraison_max (integer) - En jours
   - actif (boolean)
   - created_at, updated_at
   ```

3. **Table `produit_zones_livraison`**:
   ```
   - id (PK)
   - produit_id (FK)
   - marchand_zone_livraison_id (FK)
   - frais_livraison_specifique (decimal, nullable) - Remplace les frais standard si défini
   - actif (boolean)
   - created_at, updated_at
   ```

#### 1.2 Modification des tables existantes

1. **Table `adresses`**:
   - Ajouter `zone_livraison_id` (FK, nullable)

2. **Table `commandes`**:
   - Ajouter `frais_livraison` (decimal)
   - Ajouter `delai_livraison_estime` (integer) - En jours

### 2. Interface d'administration pour les marchands

#### 2.1 Gestion des zones de livraison

1. Créer une interface dans le tableau de bord marchand pour:
   - Voir toutes les zones disponibles (pays, régions, villes)
   - Sélectionner les zones où le marchand peut livrer
   - Définir les frais de livraison pour chaque zone
   - Définir les délais de livraison estimés

2. Permettre la création de zones personnalisées (quartiers spécifiques)

#### 2.2 Association produits-zones

1. Ajouter un onglet "Livraison" dans le formulaire de création/édition de produit:
   - Option par défaut: "Utiliser les paramètres de livraison du marchand"
   - Option personnalisée: Sélectionner des zones spécifiques pour ce produit
   - Possibilité de définir des frais de livraison spécifiques par produit et par zone

### 3. Détection de la localisation client

#### 3.1 Méthodes de détection

1. **Adresse enregistrée**:
   - Utiliser l'adresse de livraison enregistrée dans le compte client

2. **Détection automatique**:
   - Utiliser l'API de géolocalisation pour détecter le pays/ville
   - Proposer au client de confirmer ou modifier cette localisation

3. **Sélection manuelle**:
   - Permettre au client de sélectionner manuellement sa localisation

#### 3.2 Stockage de la localisation

1. Stocker la localisation dans:
   - La session utilisateur (pour les visiteurs non connectés)
   - Le profil utilisateur (pour les utilisateurs connectés)

### 4. Filtrage des produits

#### 4.1 Logique de filtrage

1. Créer une requête qui:
   - Récupère la localisation du client
   - Trouve les zones de livraison correspondantes
   - Filtre les produits qui peuvent être livrés dans ces zones

2. Implémenter cette logique dans:
   - La page d'accueil
   - Les pages de catégories
   - Les résultats de recherche

#### 4.2 Affichage des informations de livraison

1. Sur la page produit:
   - Afficher les frais de livraison pour la localisation actuelle
   - Afficher le délai de livraison estimé
   - Afficher un message si le produit ne peut pas être livré à l'adresse actuelle

2. Dans le panier:
   - Calculer les frais de livraison totaux
   - Vérifier la compatibilité de tous les produits avec l'adresse de livraison

### 5. Processus de commande

#### 5.1 Validation de la commande

1. Avant de finaliser la commande:
   - Vérifier que tous les produits peuvent être livrés à l'adresse sélectionnée
   - Calculer les frais de livraison définitifs
   - Afficher le délai de livraison estimé

2. Si certains produits ne peuvent pas être livrés:
   - Proposer de les retirer du panier
   - Suggérer des alternatives livrables

#### 5.2 Suivi de livraison

1. Permettre au marchand de:
   - Mettre à jour le statut de la livraison
   - Fournir un numéro de suivi
   - Ajuster le délai de livraison si nécessaire

2. Permettre au client de:
   - Suivre l'état de sa commande
   - Recevoir des notifications sur l'avancement de la livraison

### 6. Rapports et analyses

1. Pour les marchands:
   - Statistiques sur les zones les plus demandées
   - Analyse des coûts de livraison
   - Taux de conversion par zone

2. Pour les administrateurs:
   - Vue d'ensemble des performances de livraison
   - Identification des zones mal desservies

## Plan d'implémentation technique

### Phase 1: Structure de base
1. Créer les migrations pour les nouvelles tables
2. Développer les modèles et relations
3. Implémenter l'interface d'administration des zones pour les marchands

### Phase 2: Gestion des produits
1. Modifier le formulaire de produit pour inclure les options de livraison
2. Développer la logique d'association produit-zone

### Phase 3: Détection et filtrage
1. Implémenter la détection de localisation
2. Développer la logique de filtrage des produits
3. Adapter l'interface utilisateur pour afficher les informations de livraison

### Phase 4: Processus de commande
1. Intégrer la vérification de livraison dans le processus de commande
2. Développer le système de suivi de livraison

### Phase 5: Tests et optimisation
1. Tester le système avec différents scénarios
2. Optimiser les performances des requêtes de filtrage
3. Recueillir les retours des marchands et clients

## Considérations supplémentaires

1. **Internationalisation**:
   - Support multilingue pour les noms de zones
   - Conversion des devises pour les frais de livraison

2. **Évolutivité**:
   - Structure permettant d'ajouter facilement de nouvelles zones
   - Possibilité d'intégrer des services de livraison tiers à l'avenir

3. **Performances**:
   - Mise en cache des zones disponibles
   - Optimisation des requêtes de filtrage pour éviter les ralentissements
