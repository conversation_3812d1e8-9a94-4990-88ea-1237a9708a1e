<?php

namespace App\Filament\Marchand\Resources\CommandeResource\Pages;

use App\Filament\Marchand\Resources\CommandeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCommandes extends ListRecords
{
    protected static string $resource = CommandeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Les marchands ne peuvent pas créer de commandes manuellement
        ];
    }
}
