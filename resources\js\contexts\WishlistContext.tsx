import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Product } from '@/models/Product';
import { WishlistService } from '@/services/WishlistService';

interface WishlistContextType {
  items: Product[];
  addItem: (product: Product) => boolean;
  removeItem: (productId: string) => boolean;
  clearWishlist: () => void;
  isInWishlist: (productId: string) => boolean;
  itemCount: number;
  toggleWishlist: (product: Product) => boolean;
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

/**
 * Hook personnalisé pour utiliser le contexte de la wishlist
 */
export const useWishlist = (): WishlistContextType => {
  const context = useContext(WishlistContext);
  if (!context) {
    throw new Error('useWishlist doit être utilisé à l\'intérieur d\'un WishlistProvider');
  }
  return context;
};

interface WishlistProviderProps {
  children: ReactNode;
}

/**
 * Fournisseur de contexte pour la wishlist
 */
export const WishlistProvider: React.FC<WishlistProviderProps> = ({ children }) => {
  // Service de wishlist
  const wishlistService = new WishlistService();
  
  // État local pour les éléments de la wishlist
  const [items, setItems] = useState<Product[]>([]);
  const [itemCount, setItemCount] = useState(0);

  // Charger la wishlist au montage du composant
  useEffect(() => {
    refreshWishlist();
  }, []);

  /**
   * Rafraîchit les données de la wishlist depuis le service
   */
  const refreshWishlist = () => {
    const wishlistItems = wishlistService.getItems();
    setItems(wishlistItems);
    setItemCount(wishlistItems.length);
  };

  /**
   * Ajoute un produit à la wishlist
   * 
   * @param product - Le produit à ajouter
   * @returns true si le produit a été ajouté, false s'il était déjà présent
   */
  const addItem = (product: Product): boolean => {
    const result = wishlistService.addItem(product);
    refreshWishlist();
    return result;
  };

  /**
   * Supprime un produit de la wishlist
   * 
   * @param productId - L'identifiant du produit à supprimer
   * @returns true si le produit a été supprimé, false s'il n'était pas présent
   */
  const removeItem = (productId: string): boolean => {
    const result = wishlistService.removeItem(productId);
    refreshWishlist();
    return result;
  };

  /**
   * Vide complètement la wishlist
   */
  const clearWishlist = (): void => {
    wishlistService.clearWishlist();
    refreshWishlist();
  };

  /**
   * Vérifie si un produit est dans la wishlist
   * 
   * @param productId - L'identifiant du produit à vérifier
   * @returns true si le produit est dans la wishlist, false sinon
   */
  const isInWishlist = (productId: string): boolean => {
    return wishlistService.isInWishlist(productId);
  };

  /**
   * Bascule l'état d'un produit dans la wishlist (ajoute ou supprime)
   * 
   * @param product - Le produit à ajouter ou supprimer
   * @returns true si le produit a été ajouté, false s'il a été supprimé
   */
  const toggleWishlist = (product: Product): boolean => {
    if (isInWishlist(product.id)) {
      removeItem(product.id);
      return false;
    } else {
      addItem(product);
      return true;
    }
  };

  // Valeur du contexte exposée aux composants
  const value = {
    items,
    addItem,
    removeItem,
    clearWishlist,
    isInWishlist,
    itemCount,
    toggleWishlist,
  };

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  );
};
