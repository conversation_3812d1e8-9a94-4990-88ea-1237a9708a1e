import { useState, useEffect, useMemo } from 'react';
import { Link, router } from '@inertiajs/react';
import { Category } from '@/models/Category';
import { CategoryService } from '@/services/CategoryService';
import { ChevronDown, ChevronUp, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';

interface SubcategoryFilterProps {
  categoryId: string;
  categorySlug: string;
  selectedSubcategories?: string[];
  className?: string;
  onFilterChange?: (subcategories: string[]) => void;
  onReset?: () => void; // Fonction de réinitialisation externe
}

export default function SubcategoryFilter({
  categoryId,
  categorySlug,
  selectedSubcategories = [],
  className = '',
  onFilterChange,
  onReset,
}: SubcategoryFilterProps) {
  const [subcategories, setSubcategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isExpanded, setIsExpanded] = useState(true);
  const [selected, setSelected] = useState<Set<string>>(new Set(selectedSubcategories));
  const categoryService = useMemo(() => new CategoryService(), []);

  // Mettre à jour l'état local lorsque les props selectedSubcategories changent
  useEffect(() => {
    setSelected(new Set(selectedSubcategories));
  }, [selectedSubcategories]);

  // État pour suivre les sous-catégories développées
  const [expandedSubcategories, setExpandedSubcategories] = useState<Set<string>>(new Set());

  // Récupérer les sous-catégories de manière récursive
  useEffect(() => {
    const fetchSubcategories = async () => {
      setIsLoading(true);
      try {
        // Récupérer les sous-catégories de manière récursive
        const subs = await categoryService.getSubcategories(categoryId, true);
        setSubcategories(subs);
      } catch (error) {
        console.error(`Erreur lors de la récupération des sous-catégories:`, error);
        setSubcategories([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubcategories();
  }, [categoryId, categoryService]);

  // Gérer la sélection d'une sous-catégorie
  const handleSubcategoryChange = (subcategoryId: string, checked: boolean) => {
    const newSelected = new Set(selected);

    if (checked) {
      newSelected.add(subcategoryId);
    } else {
      newSelected.delete(subcategoryId);
    }

    setSelected(newSelected);

    // Convertir le Set en tableau
    const selectedSubcategoriesArray = Array.from(newSelected);

    // Appeler la fonction de callback si elle existe
    if (onFilterChange) {
      onFilterChange(selectedSubcategoriesArray);
    }

    // Mettre à jour l'URL avec les sous-catégories sélectionnées
    router.get(route('category', {
      categorySlug,
      subcategories: selectedSubcategoriesArray.join(',')
    }), {}, { preserveState: true, preserveScroll: true });
  };

  // Basculer l'état d'expansion d'une sous-catégorie
  const toggleSubcategory = (subcategoryId: string) => {
    const newExpanded = new Set(expandedSubcategories);

    if (newExpanded.has(subcategoryId)) {
      newExpanded.delete(subcategoryId);
    } else {
      newExpanded.add(subcategoryId);
    }

    setExpandedSubcategories(newExpanded);
  };

  // Composant récursif pour afficher une sous-catégorie et ses enfants
  const renderSubcategory = (subcategory: Category, level: number = 0) => {
    return (
      <div key={subcategory.id} className="space-y-2">
        <div className="flex items-center gap-2">
          <Checkbox
            id={`subcategory-${subcategory.id}`}
            checked={selected.has(subcategory.id)}
            onCheckedChange={(checked) =>
              handleSubcategoryChange(subcategory.id, checked === true)
            }
          />
          <label
            htmlFor={`subcategory-${subcategory.id}`}
            className="cursor-pointer text-sm flex-grow"
          >
            {subcategory.getTranslatedName()}
          </label>

          {/* Afficher le chevron uniquement si la sous-catégorie a des enfants */}
          {subcategory.hasChildren() && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                toggleSubcategory(subcategory.id);
              }}
            >
              {expandedSubcategories.has(subcategory.id) ? (
                <ChevronUp className="h-3 w-3" />
              ) : (
                <ChevronDown className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>

        {/* Afficher les sous-catégories enfants si la sous-catégorie est développée */}
        {subcategory.hasChildren() && expandedSubcategories.has(subcategory.id) && (
          <div className={`space-y-2 border-l-2 border-muted pl-2 ${level < 3 ? 'ml-6' : 'ml-4'}`}>
            {subcategory.children.map((child) => renderSubcategory(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  // Fonction pour réinitialiser les sous-catégories sélectionnées
  const resetSubcategories = () => {
    // Réinitialiser l'état local
    setSelected(new Set());

    // Appeler la fonction de callback si elle existe
    if (onFilterChange) {
      onFilterChange([]);
    }

    // Si une fonction de réinitialisation externe est fournie, l'appeler
    if (onReset) {
      onReset();
      return; // Ne pas continuer si la fonction externe est appelée
    }

    // Sinon, mettre à jour l'URL sans sous-catégories
    router.get(route('category', {
      categorySlug
    }), {}, { preserveState: true, preserveScroll: true });
  };

  // Si aucune sous-catégorie n'est disponible, ne pas afficher le filtre
  if (!isLoading && subcategories.length === 0) {
    return null;
  }

  return (
    <div className={`rounded-lg border bg-card p-4 shadow-sm ${className}`}>
      <div className="mb-2 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4" />
          <h3 className="font-medium">Sous-catégories</h3>
        </div>
        <div className="flex items-center gap-1">
          {/* Bouton de réinitialisation - visible uniquement si des sous-catégories sont sélectionnées */}
          {selected.size > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 text-xs px-2"
              onClick={resetSubcategories}
            >
              Réinitialiser
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      <Separator className="my-2" />

      {isExpanded && (
        <div className="space-y-2">
          {isLoading ? (
            <div className="space-y-2">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-pulse rounded bg-muted"></div>
                  <div className="h-4 w-24 animate-pulse rounded bg-muted"></div>
                </div>
              ))}
            </div>
          ) : (
            subcategories.map((subcategory) => renderSubcategory(subcategory))
          )}
        </div>
      )}
    </div>
  );
}
