<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reviews', function (Blueprint $table) {
            $table->id();
            $table->foreignId('produit_id')->constrained('produits')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('name', 100); // Nom de l'utilisateur (pour les non-inscrits)
            $table->string('email', 255)->nullable(); // Email de l'utilisateur (pour les non-inscrits)
            $table->tinyInteger('rating')->unsigned(); // Note de 1 à 5
            $table->text('comment'); // Commentaire
            $table->json('images')->nullable(); // Images jointes (JSON)
            $table->integer('likes')->default(0); // Nombre de likes
            $table->integer('dislikes')->default(0); // Nombre de dislikes
            $table->string('ip_address', 45)->nullable(); // Adresse IP de l'utilisateur
            $table->boolean('is_approved')->default(false); // Si l'avis est approuvé
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reviews');
    }
};
