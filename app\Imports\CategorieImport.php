<?php

namespace App\Imports;

use App\Helpers\ImageStorage;
use App\Models\Categorie;
use App\Services\SpreadsheetImporter;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class CategorieImport
{
    /**
     * @var array
     */
    protected $results = [
        'total' => 0,
        'created' => 0,
        'updated' => 0,
        'errors' => [],
        'warnings' => [],
        'categories' => [], // Pour stocker les catégories à afficher dans le modal
    ];

    /**
     * @var array
     */
    protected $categoriesMap = [];

    /**
     * Normalise une chaîne pour la comparaison
     *
     * @param string $string La chaîne à normaliser
     * @return string La chaîne normalisée
     */
    protected function normalizeString($string): string
    {
        // Convertir en minuscules
        $string = mb_strtolower($string);

        // Supprimer les espaces multiples
        $string = preg_replace('/\s+/', ' ', $string);

        return $string;
    }



    /**
     * Importe les catégories à partir d'un fichier
     *
     * @param string $filePath Chemin du fichier à importer
     * @return array Les données du fichier importé
     */
    public function import(string $filePath)
    {
        // Importer les données du fichier
        $rows = SpreadsheetImporter::import($filePath);
        // Charger toutes les catégories existantes pour la vérification
        $existingCategories = Categorie::all();

        // Préparer les données pour l'affichage dans le modal
        $categories = [];
        foreach ($rows as $index => $row) {
            // Vérifier si la catégorie existe déjà
            $exists = false;
            $similarTo = null;

            foreach ($existingCategories as $existingCategory) {
                $existingNom = $existingCategory->nom;
                $existingFrName = is_array($existingNom) ? ($existingNom['fr'] ?? '') : $existingNom;
                $existingEnName = is_array($existingNom) ? ($existingNom['en'] ?? '') : '';

                $rowFrName = $row['nom_fr'];
                $rowEnName = $row['nom_en'] ?? '';

                // Normaliser les noms pour la comparaison
                $existingFrNameNormalized = $this->normalizeString($existingFrName);
                $existingEnNameNormalized = !empty($existingEnName) ? $this->normalizeString($existingEnName) : '';
                $rowFrNameNormalized = $this->normalizeString($rowFrName);
                $rowEnNameNormalized = !empty($rowEnName) ? $this->normalizeString($rowEnName) : '';

                // Vérifier si la catégorie existe déjà (correspondance exacte)
                if ($rowFrNameNormalized === $existingFrNameNormalized ||
                    (!empty($rowEnNameNormalized) && $rowEnNameNormalized === $existingFrNameNormalized) ||
                    (!empty($existingEnNameNormalized) && $rowFrNameNormalized === $existingEnNameNormalized) ||
                    (!empty($rowEnNameNormalized) && !empty($existingEnNameNormalized) && $rowEnNameNormalized === $existingEnNameNormalized)) {
                    $exists = true;
                    $similarTo = $existingFrName;
                    break;
                }
            }

            // Ajouter la catégorie à la liste avec son statut
            // S'assurer que les chaînes sont correctement encodées en UTF-8
            $categories[] = [
                'nom_fr' => $row['nom_fr'],
                'nom_en' => $row['nom_en'] ?? '',
                'description_fr' => $row['description_fr'] ?? '',
                'description_en' => $row['description_en'] ?? '',
                'categorie_parent' => $row['categorie_parent'] ?? '',
                'image_url' => $row['image_url'] ?? '',
                'exists' => $exists,
                'similar_to' => $similarTo ? $similarTo : null,
                'row_index' => $index + 2, // +2 car la première ligne est l'en-tête et les indices commencent à 0
            ];
        }

        // Stocker les catégories dans les résultats
        $this->results['categories'] = $categories;
        $this->results['total'] = count($categories);

        return $this->results;
    }

    /**
     * Valide les données d'une ligne
     *
     * @param array $row Données à valider
     * @return array Erreurs de validation (tableau vide si aucune erreur)
     */
    protected function validateRow(array $row): array
    {
        $errors = [];

        // Vérifier les champs obligatoires
        if (empty($row['nom_fr'])) {
            $errors[] = 'Le nom en français est obligatoire';
        } elseif (strlen($row['nom_fr']) > 255) {
            $errors[] = 'Le nom en français ne doit pas dépasser 255 caractères';
        }

        if (!empty($row['nom_en']) && strlen($row['nom_en']) > 255) {
            $errors[] = 'Le nom en anglais ne doit pas dépasser 255 caractères';
        }

        // Vérifier l'URL de l'image si elle est fournie
        if (!empty($row['image_url'])) {
            $imageUrl = trim($row['image_url']);

            // Si c'est une URL externe (commence par http:// ou https://)
            if (preg_match('/^https?:\/\//', $imageUrl)) {
                // Vérifier si l'URL est valide
                if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                    $errors[] = 'L\'URL de l\'image n\'est pas valide';
                }

                // Vérifier si l'URL commence par https://
                if (strpos($imageUrl, 'https://') !== 0) {
                    // Ce n'est pas une erreur bloquante, juste un avertissement
                    // L'URL sera convertie en https:// lors de l'importation
                }
            }
        }

        return $errors;
    }

    /**
     * Importe réellement les catégories après confirmation
     *
     * @param string $filePath Chemin du fichier à importer
     * @return array Résultats de l'importation
     */
    public function importConfirmed(string $filePath)
    {
        // Réinitialiser les résultats
        $this->results = [
            'total' => 0,
            'created' => 0,
            'updated' => 0,
            'errors' => [],
            'warnings' => [],
        ];

        // Importer les données du fichier
        $rows = SpreadsheetImporter::import($filePath);

        // Charger toutes les catégories existantes pour la mise à jour
        $existingCategories = Categorie::all();

        // Créer une carte combinée pour une recherche plus efficace
        $categoriesByAllNames = collect();
        foreach ($existingCategories as $category) {
            $nom = $category->nom;
            if (is_array($nom)) {
                if (isset($nom['fr']) && !empty($nom['fr'])) {
                    $categoriesByAllNames[$this->normalizeString($nom['fr'])] = $category;
                }
                if (isset($nom['en']) && !empty($nom['en'])) {
                    $categoriesByAllNames[$this->normalizeString($nom['en'])] = $category;
                }
            } else {
                $categoriesByAllNames[$this->normalizeString($nom)] = $category;
            }
        }

        // Créer une carte des IDs de catégories par nom (pour les relations parent-enfant)
        $this->categoriesMap = [];

        // Trier les lignes pour traiter d'abord les catégories sans parent
        $sortedRows = [];
        $childRows = [];

        foreach ($rows as $index => $row) {
            if (empty($row['categorie_parent'])) {
                $sortedRows[] = ['index' => $index, 'row' => $row];
            } else {
                $childRows[] = ['index' => $index, 'row' => $row];
            }
        }

        // Ajouter les catégories enfants après les catégories parentes
        $sortedRows = array_merge($sortedRows, $childRows);

        // Première passe : créer toutes les catégories
        foreach ($sortedRows as $sortedRow) {
            $index = $sortedRow['index'];
            $row = $sortedRow['row'];
            $this->results['total']++;

            // Valider les données de la ligne
            $errors = $this->validateRow($row);
            if (!empty($errors)) {
                foreach ($errors as $error) {
                    $this->results['errors'][] = "Ligne " . ($index + 2) . ": " . $error;
                }
                continue;
            }

            // Préparer les données pour la création/mise à jour
            // S'assurer que les chaînes sont correctement encodées en UTF-8
            $nomFr = $row['nom_fr'];
            $nomEn = $row['nom_en'] ?? $row['nom_fr'];
            $descFr = $row['description_fr'] ?? '';
            $descEn = $row['description_en'] ?? ($row['description_fr'] ?? '');

            $nom = [
                'fr' => $nomFr,
                'en' => $nomEn,
            ];

            $description = [
                'fr' => $descFr,
                'en' => $descEn,
            ];

            // Préparer les noms pour la recherche
            $searchNomFr = trim($row['nom_fr']);
            $searchNomEn = trim($row['nom_en'] ?? '');

            // Normaliser les noms pour la recherche
            $nomFrNormalized = $this->normalizeString($searchNomFr);
            $nomEnNormalized = !empty($searchNomEn) ? $this->normalizeString($searchNomEn) : '';

            // Vérifier si la catégorie existe déjà en utilisant la carte combinée
            $existingCategory = null;

            // Chercher par nom français normalisé
            if ($categoriesByAllNames->has($nomFrNormalized)) {
                $existingCategory = $categoriesByAllNames->get($nomFrNormalized);
            }
            // Si pas trouvé et que le nom anglais est fourni, chercher par nom anglais normalisé
            elseif (!empty($nomEnNormalized) && $categoriesByAllNames->has($nomEnNormalized)) {
                $existingCategory = $categoriesByAllNames->get($nomEnNormalized);
            }

            // Vérification supplémentaire pour les noms similaires
            if (!$existingCategory) {
                // Parcourir toutes les catégories existantes pour une comparaison plus flexible
                foreach ($existingCategories as $category) {
                    $nom_cat = $category->nom;
                    $existingFrName = is_array($nom_cat) ? ($nom_cat['fr'] ?? '') : $nom_cat;
                    $existingEnName = is_array($nom_cat) ? ($nom_cat['en'] ?? '') : '';

                    // Normaliser les noms existants
                    $existingFrNameNormalized = $this->normalizeString($existingFrName);
                    $existingEnNameNormalized = !empty($existingEnName) ? $this->normalizeString($existingEnName) : '';

                    // Comparer les noms normalisés
                    if ($nomFrNormalized === $existingFrNameNormalized ||
                        (!empty($nomEnNormalized) && $nomEnNormalized === $existingFrNameNormalized) ||
                        (!empty($existingEnNameNormalized) && $nomFrNormalized === $existingEnNameNormalized) ||
                        (!empty($nomEnNormalized) && !empty($existingEnNameNormalized) && $nomEnNormalized === $existingEnNameNormalized)) {
                        $existingCategory = $category;
                        $this->results['warnings'][] = "Ligne " . ($index + 2) . ": Catégorie '{$nomFr}' associée à '{$existingFrName}' (correspondance approximative)";
                        break;
                    }
                }
            }

            if ($existingCategory) {
                // Mettre à jour la catégorie existante
                $existingCategory->nom = $nom;
                $existingCategory->description = $description;

                // Vérifier et valider l'URL de l'image
                if (!empty($row['image_url'])) {
                    $imageUrl = trim($row['image_url']);

                    // Vérifier si l'URL commence par https ou http
                    if (preg_match('/^https?:\/\//', $imageUrl)) {
                        // Si c'est une URL externe, vérifier si elle existe
                        if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                            // Convertir http en https si nécessaire
                            if (strpos($imageUrl, 'http://') === 0) {
                                $imageUrl = 'https://' . substr($imageUrl, 7);
                                $this->results['warnings'][] = "Ligne " . ($index + 2) . ": URL convertie de HTTP à HTTPS";
                            }

                            // Vérifier si le domaine est accessible
                            if ($this->isDomainAccessible($imageUrl)) {
                                // Télécharger et stocker l'image localement
                                $storedImagePath = $this->downloadAndStoreImage($imageUrl, $existingCategory->id);

                                if ($storedImagePath) {
                                    $existingCategory->image_url = $storedImagePath;
                                    $this->results['warnings'][] = "Ligne " . ($index + 2) . ": Image téléchargée et stockée localement";
                                } else {
                                    $this->results['warnings'][] = "Ligne " . ($index + 2) . ": Impossible de télécharger l'image, URL conservée";
                                    $existingCategory->image_url = $imageUrl;
                                }
                            } else {
                                $this->results['warnings'][] = "Ligne " . ($index + 2) . ": URL d'image potentiellement inaccessible, URL conservée";
                                $existingCategory->image_url = $imageUrl;
                            }
                        } else {
                            $this->results['warnings'][] = "Ligne " . ($index + 2) . ": URL d'image invalide, champ laissé vide";
                        }
                    } else {
                        // Si c'est un chemin local, l'accepter tel quel
                        $existingCategory->image_url = $imageUrl;
                    }
                }

                $existingCategory->save();

                $this->categoriesMap[$searchNomFr] = $existingCategory->id;
                $this->results['updated']++;
            } else {
                // Créer une nouvelle catégorie
                $category = new Categorie();
                $category->nom = $nom;
                $category->description = $description;

                // Vérifier et valider l'URL de l'image
                if (!empty($row['image_url'])) {
                    $imageUrl = trim($row['image_url']);

                    // Vérifier si l'URL commence par https ou http
                    if (preg_match('/^https?:\/\//', $imageUrl)) {
                        // Si c'est une URL externe, vérifier si elle existe
                        if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                            // Convertir http en https si nécessaire
                            if (strpos($imageUrl, 'http://') === 0) {
                                $imageUrl = 'https://' . substr($imageUrl, 7);
                                $this->results['warnings'][] = "Ligne " . ($index + 2) . ": URL convertie de HTTP à HTTPS";
                            }

                            // Pour les nouvelles catégories, nous devons d'abord sauvegarder la catégorie pour obtenir son ID
                            $category->save();

                            // Vérifier si le domaine est accessible
                            if ($this->isDomainAccessible($imageUrl)) {
                                // Télécharger et stocker l'image localement
                                $storedImagePath = $this->downloadAndStoreImage($imageUrl, $category->id);

                                if ($storedImagePath) {
                                    $category->image_url = $storedImagePath;
                                    $this->results['warnings'][] = "Ligne " . ($index + 2) . ": Image téléchargée et stockée localement";
                                } else {
                                    $this->results['warnings'][] = "Ligne " . ($index + 2) . ": Impossible de télécharger l'image, URL conservée";
                                    $category->image_url = $imageUrl;
                                }
                            } else {
                                $this->results['warnings'][] = "Ligne " . ($index + 2) . ": URL d'image potentiellement inaccessible, URL conservée";
                                $category->image_url = $imageUrl;
                            }

                            // Mettre à jour la catégorie avec le nouveau chemin d'image
                            $category->save();

                            // Ajouter l'ID de la catégorie à la carte des catégories
                            $this->categoriesMap[$searchNomFr] = $category->id;
                            $this->results['created']++;

                            // Continuer avec la prochaine ligne
                            continue;
                        } else {
                            $this->results['warnings'][] = "Ligne " . ($index + 2) . ": URL d'image invalide, champ laissé vide";
                        }
                    } else {
                        // Si c'est un chemin local, l'accepter tel quel
                        $category->image_url = $imageUrl;
                    }
                    // Si nous arrivons ici, c'est que nous n'avons pas d'URL d'image ou que c'est un chemin local
                    $category->save();

                    $this->categoriesMap[$searchNomFr] = $category->id;
                    $this->results['created']++;
                }
            }
        }

        // Deuxième passe : mettre à jour les relations parent-enfant
        foreach ($sortedRows as $sortedRow) {
            $index = $sortedRow['index'];
            $row = $sortedRow['row'];
            if (empty($row['categorie_parent'])) {
                continue;
            }

            // Préparer les noms pour la recherche
            $searchNomFr = trim($row['nom_fr']);
            $parentNom = trim($row['categorie_parent']);

            // Normaliser le nom de la catégorie parente
            $parentNomNormalized = $this->normalizeString($parentNom);

            // Vérifier si la catégorie parente existe avec des variations de casse
            $parentNomLower = mb_strtolower($parentNom);
            $parentNomUpper = mb_strtoupper($parentNom);
            $parentNomUcfirst = ucfirst($parentNomLower);

            // Créer un tableau de variations possibles du nom parent
            $parentNomVariations = [
                $parentNom,
                $parentNomLower,
                $parentNomUpper,
                $parentNomUcfirst
            ];

            // Vérifier si la catégorie parente existe avec l'une des variations de nom
            $parentFound = false;
            $parentId = null;

            // Vérifier d'abord les variations exactes
            foreach ($parentNomVariations as $variation) {
                if (isset($this->categoriesMap[$variation])) {
                    $parentId = $this->categoriesMap[$variation];
                    $parentFound = true;
                    break;
                }
            }

            // Si toujours pas trouvé, essayer une recherche insensible à la casse avec normalisation
            if (!$parentFound) {
                // Créer une carte temporaire des noms normalisés
                $normalizedCategoriesMap = [];
                foreach ($this->categoriesMap as $nom => $id) {
                    $normalizedCategoriesMap[$this->normalizeString($nom)] = $id;
                }

                // Vérifier si le nom normalisé existe dans la carte
                if (isset($normalizedCategoriesMap[$parentNomNormalized])) {
                    $parentId = $normalizedCategoriesMap[$parentNomNormalized];
                    $parentFound = true;
                } else {
                    // Recherche plus approfondie
                    foreach ($this->categoriesMap as $nom => $id) {
                        if ($this->normalizeString($nom) === $parentNomNormalized) {
                            $parentId = $id;
                            $parentFound = true;
                            break;
                        }
                    }

                    // Si toujours pas trouvé, rechercher dans la base de données
                    if (!$parentFound) {
                        foreach ($parentNomVariations as $variation) {
                            $parentCategory = Categorie::where('nom->fr', $variation)
                                ->orWhere('nom->en', $variation)
                                ->first();

                            if ($parentCategory) {
                                $parentId = $parentCategory->id;
                                $this->categoriesMap[$variation] = $parentId;
                                $parentFound = true;
                                break;
                            }
                        }
                    }
                }

                if (!$parentFound) {
                    $this->results['errors'][] = "Ligne " . ($index + 2) . ": Catégorie parente '{$parentNom}' introuvable";
                    continue;
                }
            }

            // Mettre à jour la relation parent-enfant
            if (!isset($this->categoriesMap[$searchNomFr])) {
                $this->results['errors'][] = "Ligne " . ($index + 2) . ": Catégorie enfant '{$searchNomFr}' introuvable";
                continue;
            }

            $categoryId = $this->categoriesMap[$searchNomFr];

            // Éviter les références circulaires
            if ($categoryId === $parentId) {
                $this->results['errors'][] = "Ligne " . ($index + 2) . ": Une catégorie ne peut pas être sa propre parente";
                continue;
            }

            $category = Categorie::find($categoryId);
            $category->categorie_parent_id = $parentId;
            $category->save();
        }

        return $this->results;
    }



    /**
     * Vérifie si le domaine d'une URL est accessible
     *
     * @param string $url L'URL à vérifier
     * @return bool True si le domaine est accessible, false sinon
     */
    protected function isDomainAccessible(string $url): bool
    {
        try {
            // Extraire le domaine de l'URL
            $parsedUrl = parse_url($url);
            if (!isset($parsedUrl['host'])) {
                return false;
            }

            $domain = $parsedUrl['host'];

            // Vérifier si le domaine répond
            $domainUrl = 'https://' . $domain;

            // Utiliser cURL pour vérifier le domaine avec un timeout court
            $ch = curl_init($domainUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_NOBODY, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5); // Timeout de 5 secondes
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Ne pas vérifier le certificat SSL
            curl_exec($ch);

            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            // Si le code HTTP est entre 200 et 399, le domaine est accessible
            return $httpCode >= 200 && $httpCode < 400;
        } catch (\Exception) {
            // En cas d'erreur, considérer que le domaine n'est pas accessible
            return false;
        }
    }

    /**
     * Télécharge une image à partir d'une URL et la stocke dans le système
     *
     * @param string $imageUrl L'URL de l'image à télécharger
     * @param int $categoryId L'ID de la catégorie
     * @return string|null Le chemin relatif de l'image stockée ou null en cas d'erreur
     */
    protected function downloadAndStoreImage(string $imageUrl, int $categoryId): ?string
    {
        try {
            // Vérifier si l'URL est valide
            if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                return null;
            }

            // Télécharger l'image sans vérifier le certificat SSL
            $response = Http::timeout(10)->withoutVerifying()->get($imageUrl);
            if (!$response->successful()) {
                $this->results['warnings'][] = "Impossible de télécharger l'image depuis {$imageUrl}";
                return null;
            }

            // Déterminer l'extension du fichier
            $contentType = $response->header('Content-Type');
            $extension = $this->getExtensionFromContentType($contentType);

            if (!$extension) {
                // Essayer de déterminer l'extension à partir de l'URL
                $extension = pathinfo(parse_url($imageUrl, PHP_URL_PATH), PATHINFO_EXTENSION);

                // Vérifier si l'extension est valide
                if (!in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
                    $extension = 'jpg'; // Extension par défaut
                }
            }

            // Créer un nom de fichier unique
            $filename = Str::uuid() . '.' . $extension;

            // Déterminer le dossier de stockage
            $folderPrefix = ImageStorage::getFolderPrefix($categoryId);
            $directory = "categories/{$folderPrefix}";

            // Créer le dossier s'il n'existe pas
            $fullPath = public_path("images/{$directory}");
            if (!file_exists($fullPath)) {
                mkdir($fullPath, 0755, true);
            }

            // Enregistrer l'image
            file_put_contents("{$fullPath}/{$filename}", $response->body());

            // Retourner le chemin relatif
            return "{$directory}/{$filename}";
        } catch (\Exception $e) {
            $this->results['warnings'][] = "Erreur lors du téléchargement de l'image: " . $e->getMessage();
            return null;
        }
    }

    /**
     * Détermine l'extension de fichier à partir du type de contenu
     *
     * @param string|null $contentType Le type de contenu
     * @return string|null L'extension de fichier ou null si non déterminable
     */
    protected function getExtensionFromContentType(?string $contentType): ?string
    {
        if (!$contentType) {
            return null;
        }

        $contentType = strtolower($contentType);

        $map = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'image/svg+xml' => 'svg',
        ];

        return $map[$contentType] ?? null;
    }

    /**
     * Récupérer les résultats de l'importation
     */
    public function getResults(): array
    {
        return $this->results;
    }
}
