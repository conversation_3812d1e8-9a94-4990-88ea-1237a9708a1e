import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import zoneLivraisonService, { ZoneLivraisonData, DeliveryAvailabilityData, DeliveryAvailabilityWithChildrenData } from '@/services/zoneLivraisonService';
import { Product } from '@/models/Product';
import { CartItem } from '@/models/CartItem';

/**
 * Interface définissant les propriétés et méthodes exposées par le contexte de livraison
 */
interface DeliveryContextType {
  selectedZone: ZoneLivraisonData | null;
  setSelectedZone: (zone: ZoneLivraisonData | null) => void;
  zonesTree: ZoneLivraisonData[];
  isLoading: boolean;
  error: string | null;
  deliveryFees: number;
  estimatedDeliveryTime: { min: number; max: number } | null;
  checkProductDelivery: (product: Product) => Promise<boolean>;
  getDeliveryInfo: (product: Product) => Promise<DeliveryAvailabilityData | null>;
  getDeliveryInfoWithChildren: (product: Product) => Promise<DeliveryAvailabilityWithChildrenData | null>;
  refreshDeliveryFees: () => Promise<void>;
}

/**
 * Contexte pour la gestion de la livraison
 */
const DeliveryContext = createContext<DeliveryContextType | undefined>(undefined);

/**
 * Hook personnalisé pour utiliser le contexte de livraison
 */
export function useDelivery() {
  const context = useContext(DeliveryContext);
  if (context === undefined) {
    throw new Error('useDelivery doit être utilisé à l\'intérieur d\'un DeliveryProvider');
  }
  return context;
}

interface DeliveryProviderProps {
  children: ReactNode;
}

/**
 * Fournisseur de contexte pour la livraison
 */
export function DeliveryProvider({ children }: DeliveryProviderProps) {
  const [selectedZone, setSelectedZone] = useState<ZoneLivraisonData | null>(null);
  const [zonesTree, setZonesTree] = useState<ZoneLivraisonData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [deliveryFees, setDeliveryFees] = useState<number>(0);
  const [estimatedDeliveryTime, setEstimatedDeliveryTime] = useState<{ min: number; max: number } | null>(null);
  const [cartItems, setCartItems] = useState<CartItem[]>([]);

  // Charger l'arborescence des zones de livraison au montage du composant
  useEffect(() => {
    const fetchZonesTree = async () => {
      try {
        setIsLoading(true);
        const data = await zoneLivraisonService.getZonesLivraisonTree();
        setZonesTree(data);

        // Récupérer la zone sélectionnée depuis le localStorage si elle existe
        const savedZoneId = localStorage.getItem('selectedDeliveryZone');
        if (savedZoneId) {
          try {
            const zoneData = await zoneLivraisonService.getZoneLivraison(parseInt(savedZoneId));
            setSelectedZone(zoneData);
          } catch (e) {
            // Si la zone n'existe plus, supprimer du localStorage
            localStorage.removeItem('selectedDeliveryZone');
          }
        }
      } catch (err) {
        setError('Erreur lors du chargement des zones de livraison');
        console.error(err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchZonesTree();
  }, []);

  // Mettre à jour le localStorage lorsque la zone sélectionnée change
  useEffect(() => {
    if (selectedZone) {
      localStorage.setItem('selectedDeliveryZone', selectedZone.id.toString());
    } else {
      localStorage.removeItem('selectedDeliveryZone');
    }
  }, [selectedZone]);

  // Vérifier si un produit peut être livré dans la zone sélectionnée
  const checkProductDelivery = useCallback(async (product: Product): Promise<boolean> => {
    if (!selectedZone || !product.id) return false;

    try {
      const result = await zoneLivraisonService.checkDeliveryAvailability(
        parseInt(product.id.toString()),
        selectedZone.id
      );
      return result.available;
    } catch (err) {
      console.error('Erreur lors de la vérification de la livraison:', err);
      return false;
    }
  }, [selectedZone]);

  // Obtenir les informations de livraison pour un produit
  const getDeliveryInfo = useCallback(async (product: Product): Promise<DeliveryAvailabilityData | null> => {
    if (!selectedZone || !product.id) return null;

    try {
      const result = await zoneLivraisonService.checkDeliveryAvailability(
        parseInt(product.id.toString()),
        selectedZone.id
      );

      console.log('API response for delivery check:', result); // Log pour déboguer

      // Vérifier que les données sont bien formatées
      if (result && typeof result === 'object') {
        return result;
      } else {
        console.error('Format de réponse inattendu:', result);
        return null;
      }
    } catch (err) {
      console.error('Erreur lors de la récupération des informations de livraison:', err);
      return null;
    }
  }, [selectedZone]);

  // Obtenir les informations de livraison pour un produit, y compris les zones enfants
  const getDeliveryInfoWithChildren = useCallback(async (product: Product): Promise<DeliveryAvailabilityWithChildrenData | null> => {
    if (!selectedZone || !product.id) return null;

    try {
      const result = await zoneLivraisonService.checkDeliveryAvailabilityWithChildren(
        parseInt(product.id.toString()),
        selectedZone.id
      );

      console.log('API response for delivery check with children:', result); // Log pour déboguer

      // Vérifier que les données sont bien formatées
      if (result && typeof result === 'object') {
        return result;
      } else {
        console.error('Format de réponse inattendu:', result);
        return null;
      }
    } catch (err) {
      console.error('Erreur lors de la récupération des informations de livraison avec enfants:', err);
      return null;
    }
  }, [selectedZone]);

  // Rafraîchir les frais de livraison pour tous les produits du panier
  const refreshDeliveryFees = useCallback(async (items: CartItem[] = cartItems): Promise<void> => {
    if (!selectedZone || items.length === 0) {
      setDeliveryFees(0);
      setEstimatedDeliveryTime(null);

      // Déclencher un événement pour informer le CartContext
      window.dispatchEvent(new CustomEvent('deliveryUpdated', {
        detail: {
          fees: 0,
          time: null
        }
      }));

      return;
    }

    try {
      // Récupérer les informations de livraison pour chaque produit du panier
      const deliveryPromises = items.map(item =>
        zoneLivraisonService.checkDeliveryAvailability(
          parseInt(item.product.id.toString()),
          selectedZone.id
        )
      );

      const deliveryResults = await Promise.all(deliveryPromises);

      // Calculer les frais de livraison totaux
      let totalFees = 0;
      let minTime = Infinity;
      let maxTime = 0;

      deliveryResults.forEach((result, index) => {
        if (result.available && result.livraison) {
          // Ajouter les frais de livraison pour ce produit
          const quantity = items[index].quantity;
          totalFees += result.livraison.frais_livraison * quantity;

          // Mettre à jour les délais de livraison min/max
          minTime = Math.min(minTime, result.livraison.delai_livraison_min);
          maxTime = Math.max(maxTime, result.livraison.delai_livraison_max);
        }
      });

      setDeliveryFees(totalFees);
      const newEstimatedTime = minTime !== Infinity ? { min: minTime, max: maxTime } : null;
      setEstimatedDeliveryTime(newEstimatedTime);

      // Déclencher un événement pour informer le CartContext
      window.dispatchEvent(new CustomEvent('deliveryUpdated', {
        detail: {
          fees: totalFees,
          time: newEstimatedTime
        }
      }));
    } catch (err) {
      console.error('Erreur lors du rafraîchissement des frais de livraison:', err);
      setDeliveryFees(0);
      setEstimatedDeliveryTime(null);

      // Déclencher un événement pour informer le CartContext
      window.dispatchEvent(new CustomEvent('deliveryUpdated', {
        detail: {
          fees: 0,
          time: null
        }
      }));
    }
  }, [selectedZone, cartItems]);

  // Mettre à jour les cartItems depuis le localStorage
  useEffect(() => {
    const updateCartItems = () => {
      try {
        const cartData = localStorage.getItem('cart');
        if (cartData) {
          const parsedCart = JSON.parse(cartData);
          if (Array.isArray(parsedCart)) {
            setCartItems(parsedCart);
          }
        }
      } catch (error) {
        console.error('Erreur lors de la récupération du panier:', error);
      }
    };

    // Mettre à jour les cartItems au chargement
    updateCartItems();

    // Écouter les changements dans le localStorage
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'cart') {
        updateCartItems();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Écouter un événement personnalisé pour les mises à jour du panier
    const handleCartUpdate = () => updateCartItems();
    window.addEventListener('cartUpdated', handleCartUpdate);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('cartUpdated', handleCartUpdate);
    };
  }, []);

  // Rafraîchir les frais de livraison lorsque le panier ou la zone sélectionnée change
  useEffect(() => {
    refreshDeliveryFees(cartItems);
  }, [cartItems, selectedZone, refreshDeliveryFees]);

  const value = {
    selectedZone,
    setSelectedZone,
    zonesTree,
    isLoading,
    error,
    deliveryFees,
    estimatedDeliveryTime,
    checkProductDelivery,
    getDeliveryInfo,
    getDeliveryInfoWithChildren,
    refreshDeliveryFees
  };

  return <DeliveryContext.Provider value={value}>{children}</DeliveryContext.Provider>;
}
