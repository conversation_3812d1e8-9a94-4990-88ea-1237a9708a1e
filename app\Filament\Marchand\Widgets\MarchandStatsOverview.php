<?php

namespace App\Filament\Marchand\Widgets;

use App\Models\Commande;
use App\Models\Produit;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class MarchandStatsOverview extends BaseWidget
{
    protected function getStats(): array
    {
        $marchandId = auth()->user()->marchands->first()->id ?? null;
        
        // Statistiques des commandes
        $totalCommandes = Commande::where('marchand_id', $marchandId)->count();
        $commandesEnCours = Commande::where('marchand_id', $marchandId)
            ->whereIn('statut', ['EnAttente', 'EnCoursDeTraitement'])
            ->count();
        $commandesLivrees = Commande::where('marchand_id', $marchandId)
            ->where('statut', 'Livré')
            ->count();
        
        // Statistiques des ventes
        $ventesTotales = Commande::where('marchand_id', $marchandId)
            ->sum('montantTotal');
        
        // Statistiques des produits
        $totalProduits = Produit::where('marchand_id', $marchandId)->count();
        $produitsEnRupture = Produit::where('marchand_id', $marchandId)
            ->where('stock', 0)
            ->count();
        
        return [
            Stat::make('Total des commandes', $totalCommandes)
                ->description('Toutes les commandes')
                ->descriptionIcon('heroicon-m-shopping-cart')
                ->color('primary'),
            
            Stat::make('Commandes en cours', $commandesEnCours)
                ->description('En attente ou en traitement')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),
            
            Stat::make('Commandes livrées', $commandesLivrees)
                ->description('Commandes complétées')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color('success'),
            
            Stat::make('Ventes totales', number_format($ventesTotales, 2) . ' €')
                ->description('Chiffre d\'affaires')
                ->descriptionIcon('heroicon-m-currency-euro')
                ->color('success'),
            
            Stat::make('Produits', $totalProduits)
                ->description('Nombre total de produits')
                ->descriptionIcon('heroicon-m-shopping-bag')
                ->color('primary'),
            
            Stat::make('Produits en rupture', $produitsEnRupture)
                ->description('À réapprovisionner')
                ->descriptionIcon('heroicon-m-exclamation-circle')
                ->color($produitsEnRupture > 0 ? 'danger' : 'success'),
        ];
    }
}
