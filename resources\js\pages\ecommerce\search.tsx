import { useState, useEffect } from 'react';
import { Head } from '@inertiajs/react';
import EcommerceLayout from '@/layouts/ecommerce-layout';
import CardProduit from '@/components/ecommerce/CardProduit';
import { ProductService } from '@/services/ProductService';
import { Product } from '@/models/Product';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, SlidersHorizontal } from 'lucide-react';

/**
 * Props pour la page de recherche
 */
interface SearchPageProps {
  query?: string;
}

/**
 * Page affichant les résultats de recherche
 *
 * @param query - Le terme de recherche
 */
export default function SearchPage({ query = '' }: SearchPageProps) {
  const [searchTerm, setSearchTerm] = useState(query);
  const [products, setProducts] = useState<Product[]>([]);
  const [sortBy, setSortBy] = useState('default');
  const [isLoading, setIsLoading] = useState(false);
  const productService = new ProductService();

  // Recherche de produits lorsque le terme de recherche change
  useEffect(() => {
    const fetchProducts = async () => {
      if (searchTerm.trim()) {
        setIsLoading(true);
        try {
          const results = await productService.searchProducts(searchTerm);
          setProducts(results);
        } catch (error) {
          console.error('Erreur lors de la recherche:', error);
        } finally {
          setIsLoading(false);
        }
      } else {
        setProducts([]);
      }
    };

    fetchProducts();
  }, [searchTerm]);

  // Tri des produits
  useEffect(() => {
    let sortedProducts = [...products];

    switch (sortBy) {
      case 'price-asc':
        sortedProducts.sort((a, b) => a.price - b.price);
        break;
      case 'price-desc':
        sortedProducts.sort((a, b) => b.price - a.price);
        break;
      case 'name-asc':
        sortedProducts.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'name-desc':
        sortedProducts.sort((a, b) => b.name.localeCompare(a.name));
        break;
      case 'rating-desc':
        sortedProducts.sort((a, b) => b.rating - a.rating);
        break;
      default:
        // Pas de tri spécifique
        break;
    }

    setProducts(sortedProducts);
  }, [sortBy]);

  /**
   * Gère la soumission du formulaire de recherche
   *
   * @param e - L'événement de soumission
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // La recherche est déjà déclenchée par l'effet useEffect
  };

  return (
    <EcommerceLayout>
      <Head title={`Recherche: ${searchTerm || 'Tous les produits'} - Lorrelei`} />

      <div className="container mx-auto px-4 py-8">
        <h1 className="mb-6 text-2xl font-bold sm:text-3xl">
          {searchTerm
            ? `Résultats pour "${searchTerm}"`
            : 'Tous les produits'}
        </h1>

        {/* Formulaire de recherche */}
        <div className="mb-8">
          <form onSubmit={handleSubmit} className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Rechercher des produits..."
                className="pl-10"
              />
            </div>
            <Button type="submit">Rechercher</Button>
          </form>
        </div>

        {/* Barre de tri */}
        <div className="mb-6 flex flex-wrap items-center justify-between gap-4">
          <p className="text-sm text-muted-foreground">
            {products.length} résultat{products.length !== 1 ? 's' : ''}
          </p>

          <div className="flex items-center gap-2">
            <SlidersHorizontal className="h-4 w-4 text-muted-foreground" />
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Trier par" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">Pertinence</SelectItem>
                <SelectItem value="price-asc">Prix croissant</SelectItem>
                <SelectItem value="price-desc">Prix décroissant</SelectItem>
                <SelectItem value="name-asc">Nom (A-Z)</SelectItem>
                <SelectItem value="name-desc">Nom (Z-A)</SelectItem>
                <SelectItem value="rating-desc">Meilleures notes</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Résultats de recherche */}
        {isLoading ? (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="animate-pulse rounded-lg border">
                <div className="aspect-square bg-muted"></div>
                <div className="p-4 space-y-3">
                  <div className="h-4 w-1/4 rounded bg-muted"></div>
                  <div className="h-4 w-3/4 rounded bg-muted"></div>
                  <div className="h-4 w-1/2 rounded bg-muted"></div>
                  <div className="h-8 rounded bg-muted"></div>
                </div>
              </div>
            ))}
          </div>
        ) : products.length > 0 ? (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {products.map((product) => (
              <CardProduit key={product.id} product={product} />
            ))}
          </div>
        ) : searchTerm ? (
          <div className="rounded-lg border border-dashed p-8 text-center">
            <h3 className="mb-2 text-lg font-medium">Aucun résultat trouvé</h3>
            <p className="text-muted-foreground">
              Aucun produit ne correspond à votre recherche "{searchTerm}".
              <br />
              Essayez avec d'autres mots-clés ou parcourez nos catégories.
            </p>
          </div>
        ) : (
          <div className="rounded-lg border border-dashed p-8 text-center">
            <h3 className="mb-2 text-lg font-medium">Commencez votre recherche</h3>
            <p className="text-muted-foreground">
              Entrez des mots-clés pour trouver les produits qui vous intéressent.
            </p>
          </div>
        )}
      </div>
    </EcommerceLayout>
  );
}
