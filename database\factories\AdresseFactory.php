<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Adresse;

class AdresseFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Adresse::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'rue' => fake()->regexify('[A-Za-z0-9]{255}'),
            'ville' => fake()->regexify('[A-Za-z0-9]{100}'),
            'etat' => fake()->regexify('[A-Za-z0-9]{100}'),
            'pays' => fake()->regexify('[A-Za-z0-9]{100}'),
            'codePostal' => fake()->regexify('[A-Za-z0-9]{20}'),
            'type' => fake()->randomElement(["Livraison","Facturation","Entreprise"]),
        ];
    }
}
