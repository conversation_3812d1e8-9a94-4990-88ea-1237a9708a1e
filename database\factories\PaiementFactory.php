<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use App\Models\Commande;
use App\Models\Marchand;
use App\Models\Paiement;

class PaiementFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Paiement::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'marchand_id' => Marchand::factory(),
            'commande_id' => Commande::factory(),
            'montant' => fake()->randomFloat(2, 0, 99999999.99),
            'statut' => fake()->randomElement(["EnAttente","Compl\u00e9t\u00e9","\u00c9chou\u00e9","Rembours\u00e9"]),
            'creeLe' => fake()->dateTime(),
            'transaction_id' => fake()->regexify('[A-Za-z0-9]{255}'),
            'methode' => fake()->regexify('[A-Za-z0-9]{100}'),
        ];
    }
}
