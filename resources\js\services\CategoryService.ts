import { Category, Translation } from '../models/Category';

/**
 * Service pour gérer les opérations liées aux catégories
 *
 * Cette classe fournit des méthodes pour récupérer et filtrer les catégories
 */
export class CategoryService {
  /**
   * Extrait le nom traduit d'une catégorie
   *
   * @param nom - Nom de la catégorie (chaîne ou objet JSON)
   * @param locale - Langue actuelle
   * @returns Le nom traduit et l'objet de traduction brut
   */
  private extractTranslatedName(nom: any, locale: string = 'fr'): { name: string, rawName: Translation | null } {
    return this.extractTranslatedValue(nom, locale, 'Catégorie sans nom');
  }

  /**
   * Extrait une valeur traduite d'un champ
   *
   * @param value - Valeur à extraire (chaîne ou objet JSON)
   * @param locale - Langue actuelle
   * @param defaultValue - Valeur par défaut si aucune traduction n'est trouvée
   * @returns La valeur traduite et l'objet de traduction brut
   */
  private extractTranslatedValue(value: any, locale: string = 'fr', defaultValue: string = ''): { name: string, rawName: Translation | null } {
    // Récupérer la langue actuelle depuis localStorage
    const currentLocale = localStorage.getItem('locale') || locale;

    try {
      // Si c'est déjà un objet, pas besoin de parser
      if (typeof value === 'object' && value !== null) {
        const translatedValue = value[currentLocale] || value.fr || defaultValue;
        return { name: translatedValue, rawName: value as Translation };
      }

      // Si c'est une chaîne, essayer de la parser comme JSON
      if (typeof value === 'string') {
        try {
          // Vérifier si la chaîne ressemble à du JSON
          if (value.trim().startsWith('{') && value.trim().endsWith('}')) {
            const translationObj = JSON.parse(value);
            if (typeof translationObj === 'object' && translationObj !== null) {
              const translatedValue = translationObj[currentLocale] || translationObj['fr'] || defaultValue;
              return { name: translatedValue, rawName: translationObj as Translation };
            }
          }
          // Si ce n'est pas du JSON, retourner la chaîne telle quelle
          return { name: value || defaultValue, rawName: null };
        } catch (error) {
          console.warn('Erreur lors du parsing de la traduction:', error);
          return { name: value || defaultValue, rawName: null };
        }
      }

      // Fallback pour les autres types
      return { name: String(value || defaultValue), rawName: null };
    } catch (error) {
      console.error('Erreur lors de l\'extraction de la valeur traduite:', error);
      return { name: defaultValue, rawName: null };
    }
  }

  /**
   * Extrait la description traduite d'une catégorie
   *
   * @param description - Description de la catégorie (chaîne ou objet JSON)
   * @param locale - Langue actuelle
   * @returns La description traduite et l'objet de traduction brut
   */
  private extractTranslatedDescription(description: any, locale: string = 'fr'): { description: string, rawDescription: Translation | null } {
    const result = this.extractTranslatedValue(description, locale, '');
    return { description: result.name, rawDescription: result.rawName };
  }

  /**
   * Récupère toutes les catégories disponibles
   *
   * @returns Une promesse qui résout avec un tableau de catégories
   */
  async getAllCategories(): Promise<Category[]> {
    try {
      const response = await fetch('/api/categories');
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des catégories');
      }
      const data = await response.json();
      return data.map((item: Record<string, unknown>) => this.mapApiCategoryToModel(item));
    } catch (error) {
      console.error('Erreur dans getAllCategories:', error);
      // En cas d'erreur, retourner un tableau vide
      return [];
    }
  }

  /**
   * Récupère une catégorie par son identifiant
   *
   * @param id - L'identifiant de la catégorie à récupérer
   * @returns Une promesse qui résout avec la catégorie ou null si elle n'existe pas
   */
  async getCategoryById(id: string): Promise<Category | null> {
    try {
      const response = await fetch(`/api/categories/${id}`);
      if (!response.ok) {
        throw new Error(`Erreur lors de la récupération de la catégorie ${id}`);
      }
      const item = await response.json();
      return this.mapApiCategoryToModel(item as Record<string, unknown>);
    } catch (error) {
      console.error(`Erreur dans getCategoryById(${id}):`, error);
      // En cas d'erreur, retourner null
      return null;
    }
  }

  /**
   * Récupère une catégorie par son slug
   *
   * @param slug - Le slug de la catégorie à récupérer
   * @returns Une promesse qui résout avec la catégorie ou null si elle n'existe pas
   */
  async getCategoryBySlug(slug: string): Promise<Category | null> {
    try {
      const response = await fetch(`/api/categories/slug/${slug}`);
      if (!response.ok) {
        throw new Error(`Erreur lors de la récupération de la catégorie avec le slug ${slug}`);
      }
      const item = await response.json();
      return this.mapApiCategoryToModel(item as Record<string, unknown>);
    } catch (error) {
      console.error(`Erreur dans getCategoryBySlug(${slug}):`, error);
      // En cas d'erreur, retourner null
      return null;
    }
  }

  /**
   * Récupère les catégories principales (sans parent)
   *
   * @returns Une promesse qui résout avec un tableau de catégories principales
   */
  async getMainCategories(): Promise<Category[]> {
    try {
      const response = await fetch('/api/categories/main');
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des catégories principales');
      }
      const data = await response.json();
      console.log(data);
      return data.map((item: Record<string, unknown>) => this.mapApiCategoryToModel(item, null));
    } catch (error) {
      console.error('Erreur dans getMainCategories:', error);
      // En cas d'erreur, retourner un tableau vide
      return [];
    }
  }

  /**
   * Récupère les sous-catégories d'une catégorie parente
   *
   * @param parentId - L'identifiant de la catégorie parente
   * @param recursive - Si true, récupère également les sous-catégories des sous-catégories
   * @returns Une promesse qui résout avec un tableau de sous-catégories
   */
  async getSubcategories(parentId: string, recursive: boolean = false): Promise<Category[]> {
    try {
      const response = await fetch(`/api/categories/${parentId}/subcategories`);
      if (!response.ok) {
        throw new Error(`Erreur lors de la récupération des sous-catégories de ${parentId}`);
      }
      const data = await response.json();
      const subcategories = data.map((item: Record<string, unknown>) => this.mapApiCategoryToModel(item, parentId));

      // Si récursif, récupérer les sous-catégories de chaque sous-catégorie
      if (recursive) {
        for (const subcategory of subcategories) {
          subcategory.children = await this.getSubcategories(subcategory.id, true);

          // Mettre à jour le niveau et le chemin de catégorie pour les enfants
          for (const child of subcategory.children) {
            child.niveau = subcategory.niveau + 1;
            child.categoryPath = `${subcategory.categoryPath}/${child.id}`;
          }
        }
      }

      return subcategories;
    } catch (error) {
      console.error(`Erreur dans getSubcategories(${parentId}):`, error);
      // En cas d'erreur, retourner un tableau vide
      return [];
    }
  }

  /**
   * Récupère les sous-catégories avec des produits en vedette
   *
   * @param parentId - L'identifiant de la catégorie parente
   * @returns Une promesse qui résout avec un tableau de sous-catégories et leurs produits en vedette
   */
  async getSubcategoriesWithFeaturedProducts(parentId: string): Promise<{ category: Category, featuredProduct: Record<string, unknown> | null }[]> {
    try {
      const response = await fetch(`/api/categories/${parentId}/subcategories/featured`);
      if (!response.ok) {
        throw new Error(`Erreur lors de la récupération des sous-catégories avec produits de ${parentId}`);
      }
      const data = await response.json();
      return data.map((item: Record<string, unknown>) => {
        // Extraire la catégorie
        const category = this.mapApiCategoryToModel(item.category as Record<string, unknown>, parentId);

        // Extraire le produit en vedette
        const featuredProduct = item.featuredProduct ? {
          id: String((item.featuredProduct as Record<string, unknown>).id || ''),
          name: String((item.featuredProduct as Record<string, unknown>).nom || ''),
          slug: String((item.featuredProduct as Record<string, unknown>).slug || ''),
          price: Number((item.featuredProduct as Record<string, unknown>).prix || 0),
          images: typeof (item.featuredProduct as Record<string, unknown>).images === 'string'
            ? JSON.parse(String((item.featuredProduct as Record<string, unknown>).images || '[]'))
            : []
        } : null;

        return {
          category,
          featuredProduct
        };
      });
    } catch (error) {
      console.error(`Erreur dans getSubcategoriesWithFeaturedProducts(${parentId}):`, error);
      // En cas d'erreur, retourner un tableau vide
      return [];
    }
  }

  /**
   * Récupère les catégories parentes d'une catégorie
   *
   * @param categoryId - L'identifiant de la catégorie
   * @returns Une promesse qui résout avec un tableau de catégories parentes
   */
  async getCategoryParents(categoryId: string): Promise<Category[]> {
    try {
      const response = await fetch(`/api/categories/${categoryId}/parents`);
      if (!response.ok) {
        throw new Error(`Erreur lors de la récupération des catégories parentes de ${categoryId}`);
      }
      const data = await response.json();
      return data.map((item: Record<string, unknown>) => this.mapApiCategoryToModel(item));
    } catch (error) {
      console.error(`Erreur dans getCategoryParents(${categoryId}):`, error);
      // Fallback aux données fictives en cas d'erreur
      return [];
    }
  }

  /**
   * Génère des catégories fictives pour les tests
   *
   * @returns Un tableau de catégories fictives
   * @private
   */
  /**
   * Convertit les données API d'une catégorie en modèle de catégorie
   *
   * @param apiCategory - Les données d'une catégorie de l'API
   * @param parentId - L'identifiant de la catégorie parente (optionnel)
   * @returns Un modèle de catégorie
   * @private
   */
  private mapApiCategoryToModel(apiCategory: Record<string, unknown>, parentId?: string | null): Category {
    // Extraire le nom et la description traduits
    const { name, rawName } = this.extractTranslatedName(apiCategory.nom);
    const { description, rawDescription } = this.extractTranslatedDescription(apiCategory.description);

    // Déterminer l'ID du parent
    const categoryParentId = parentId !== undefined
      ? parentId
      : (apiCategory.categorie_parent_id ? String(apiCategory.categorie_parent_id) : null);

    // Déterminer l'URL de l'image
    const imageUrl = apiCategory.full_image_url
      ? String(apiCategory.full_image_url)
      : (apiCategory.image_url ? String(apiCategory.image_url) : '');

    return new Category(
      String(apiCategory.id || ''),
      name,
      String(apiCategory.slug || ''),
      description,
      imageUrl,
      categoryParentId,
      rawName,
      rawDescription
    );
  }
}
