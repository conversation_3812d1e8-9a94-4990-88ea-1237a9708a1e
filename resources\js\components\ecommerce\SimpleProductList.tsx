import { Product } from '@/models/Product';
import CardProduit from './CardProduit';
import { useTranslation } from '@/hooks/use-translation';

interface SimpleProductListProps {
    products: Product[];
    className?: string;
    emptyMessage?: string;
}

/**
 * Composant simple pour afficher une liste de produits sans logique de chargement ou de pagination
 */
export default function SimpleProductList({
    products = [],
    className = '',
    emptyMessage = 'Aucun produit trouvé'
}: SimpleProductListProps) {
    const { tDefault } = useTranslation();

    // Afficher un message si aucun produit n'est trouvé
    if (products.length === 0) {
        return <div className="py-8 text-center text-muted-foreground">{emptyMessage}</div>;
    }

    return (
        <div className={className}>
            <div className="grid grid-cols-2 gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                {products.map((product) => (
                    <CardProduit key={product.id} product={product} />
                ))}
            </div>
        </div>
    );
}
