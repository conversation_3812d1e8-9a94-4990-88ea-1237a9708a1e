# Configuration Serveur pour le CDN de Lorrelei Marketplace

Ce document détaille les différentes options de configuration serveur pour mettre en place un CDN (Content Delivery Network) pour Lorrelei Marketplace, en se concentrant particulièrement sur l'utilisation d'un dossier ou serveur séparé pour le stockage des fichiers statiques.

## Table des matières

1. [Options de déploiement](#options-de-déploiement)
2. [Configuration avec un dossier dédié](#configuration-avec-un-dossier-dédié)
3. [Configuration avec un serveur séparé](#configuration-avec-un-serveur-séparé)
4. [Utilisation d'un service CDN tiers](#utilisation-dun-service-cdn-tiers)
5. [Synchronisation des fichiers](#synchronisation-des-fichiers)
6. [Optimisations de performance](#optimisations-de-performance)

## Options de déploiement

Vous disposez de plusieurs options pour déployer votre CDN, chacune avec ses avantages et inconvénients :

| Option | Description | Avantages | Inconvénients |
|--------|-------------|-----------|---------------|
| **Dossier dans le projet** | Utiliser `public/images` dans le projet | Simple à mettre en place | Moins flexible, mélange le code et les assets |
| **Dossier dédié sur le même serveur** | Utiliser un dossier comme `/var/www/cdn` | Bonne séparation, facile à configurer | Partage les ressources avec l'application principale |
| **Serveur CDN séparé** | Utiliser un serveur distinct pour les assets | Meilleure séparation, plus de flexibilité | Plus complexe à configurer et à maintenir |
| **Service CDN tiers** | Utiliser Cloudflare, AWS CloudFront, etc. | Performance optimale, gestion simplifiée | Coût supplémentaire, dépendance externe |

## Configuration avec un dossier dédié

Cette option consiste à créer un dossier dédié sur votre serveur pour stocker les fichiers du CDN.

### 1. Création du dossier

```bash
# Créer le dossier pour le CDN
sudo mkdir -p /var/www/cdn.Lorrelei.com/images
sudo mkdir -p /var/www/cdn.Lorrelei.com/images/products
sudo mkdir -p /var/www/cdn.Lorrelei.com/images/categories
sudo mkdir -p /var/www/cdn.Lorrelei.com/images/banners

# Définir les permissions
sudo chown -R www-data:www-data /var/www/cdn.Lorrelei.com
sudo chmod -R 755 /var/www/cdn.Lorrelei.com
```

### 2. Configuration Apache

Créez un fichier de configuration pour le sous-domaine CDN :

```apache
<VirtualHost *:80>
    ServerName cdn.Lorrelei.com
    DocumentRoot /var/www/cdn.Lorrelei.com
    
    <Directory /var/www/cdn.Lorrelei.com>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Cache headers
        <IfModule mod_expires.c>
            ExpiresActive On
            ExpiresByType image/jpg "access plus 1 year"
            ExpiresByType image/jpeg "access plus 1 year"
            ExpiresByType image/gif "access plus 1 year"
            ExpiresByType image/png "access plus 1 year"
            ExpiresByType image/webp "access plus 1 year"
            ExpiresByType image/svg+xml "access plus 1 year"
        </IfModule>
        
        # Compression
        <IfModule mod_deflate.c>
            AddOutputFilterByType DEFLATE image/svg+xml
        </IfModule>
        
        # Désactiver PHP pour ce domaine
        <FilesMatch "\.php$">
            Require all denied
        </FilesMatch>
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/cdn.Lorrelei.com-error.log
    CustomLog ${APACHE_LOG_DIR}/cdn.Lorrelei.com-access.log combined
</VirtualHost>
```

Activez la configuration :

```bash
sudo a2ensite cdn.Lorrelei.com.conf
sudo systemctl restart apache2
```

### 3. Configuration Nginx

```nginx
server {
    listen 80;
    server_name cdn.Lorrelei.com;
    root /var/www/cdn.Lorrelei.com;
    
    location / {
        try_files $uri $uri/ =404;
        
        # Cache headers
        expires 1y;
        add_header Cache-Control "public, max-age=31536000, immutable";
        
        # Compression
        gzip on;
        gzip_types image/svg+xml;
        
        # Désactiver PHP
        location ~ \.php$ {
            deny all;
        }
    }
    
    access_log /var/log/nginx/cdn.Lorrelei.com-access.log;
    error_log /var/log/nginx/cdn.Lorrelei.com-error.log;
}
```

Activez la configuration :

```bash
sudo ln -s /etc/nginx/sites-available/cdn.Lorrelei.com /etc/nginx/sites-enabled/
sudo nginx -t  # Vérifier la configuration
sudo systemctl restart nginx
```

### 4. Configuration Laravel

Modifiez votre fichier `config/filesystems.php` :

```php
'disks' => [
    // Autres disques...
    
    'public_images' => [
        'driver' => 'local',
        'root' => public_path('images'),
        'url' => env('APP_URL').'/images',
        'visibility' => 'public',
    ],
    
    'cdn' => [
        'driver' => 'local',
        'root' => env('CDN_ROOT', '/var/www/cdn.Lorrelei.com'),
        'url' => env('CDN_URL', env('APP_URL').'/images'),
        'visibility' => 'public',
    ],
],
```

Et dans votre fichier `.env` :

```
CDN_ROOT=/var/www/cdn.Lorrelei.com
CDN_URL=https://cdn.Lorrelei.com
CDN_ENABLED=true
```

## Configuration avec un serveur séparé

Si vous souhaitez utiliser un serveur complètement séparé pour votre CDN :

### 1. Configuration du serveur CDN

Configurez un nouveau serveur avec Apache ou Nginx comme décrit ci-dessus.

### 2. Configuration Laravel pour SFTP

Installez le package SFTP pour Laravel :

```bash
composer require league/flysystem-sftp-v3
```

Modifiez votre fichier `config/filesystems.php` :

```php
'disks' => [
    // Autres disques...
    
    'cdn' => [
        'driver' => 'sftp',
        'host' => env('CDN_SFTP_HOST'),
        'username' => env('CDN_SFTP_USERNAME'),
        'password' => env('CDN_SFTP_PASSWORD'),
        // Ou utilisez une clé SSH
        // 'privateKey' => env('CDN_SFTP_PRIVATE_KEY'),
        'root' => env('CDN_ROOT', '/var/www/cdn.Lorrelei.com'),
        'url' => env('CDN_URL'),
        'visibility' => 'public',
    ],
],
```

Et dans votre fichier `.env` :

```
CDN_SFTP_HOST=votre_serveur_cdn
CDN_SFTP_USERNAME=utilisateur
CDN_SFTP_PASSWORD=mot_de_passe
# ou
CDN_SFTP_PRIVATE_KEY=/chemin/vers/cle_privee
CDN_ROOT=/var/www/cdn.Lorrelei.com
CDN_URL=https://cdn.Lorrelei.com
CDN_ENABLED=true
```

## Utilisation d'un service CDN tiers

### 1. Cloudflare

1. Inscrivez-vous sur Cloudflare et ajoutez votre domaine
2. Configurez les enregistrements DNS pour `cdn.Lorrelei.com`
3. Activez le cache Cloudflare pour les fichiers statiques

### 2. AWS CloudFront

1. Créez un bucket S3 pour stocker vos fichiers
2. Configurez une distribution CloudFront pointant vers ce bucket
3. Configurez votre fichier `config/filesystems.php` pour utiliser S3 :

```php
'cdn' => [
    'driver' => 's3',
    'key' => env('AWS_ACCESS_KEY_ID'),
    'secret' => env('AWS_SECRET_ACCESS_KEY'),
    'region' => env('AWS_DEFAULT_REGION'),
    'bucket' => env('AWS_BUCKET'),
    'url' => env('CDN_URL'),
    'endpoint' => env('AWS_ENDPOINT'),
    'use_path_style_endpoint' => env('AWS_USE_PATH_STYLE_ENDPOINT', false),
],
```

### 3. BunnyCDN

1. Créez un compte BunnyCDN
2. Créez une Pull Zone pointant vers votre serveur d'origine
3. Configurez votre fichier `.env` :

```
CDN_URL=https://Lorrelei.b-cdn.net
CDN_ENABLED=true
```

## Synchronisation des fichiers

Pour synchroniser les fichiers entre votre application et le CDN, créez une commande Artisan :

```php
// app/Console/Commands/SyncCdn.php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Services\CdnService;

class SyncCdn extends Command
{
    protected $signature = 'cdn:sync {--force : Écraser les fichiers existants}';
    protected $description = 'Synchronise les fichiers vers le CDN';
    
    protected $cdnService;
    
    public function __construct(CdnService $cdnService)
    {
        parent::__construct();
        $this->cdnService = $cdnService;
    }
    
    public function handle()
    {
        if (!$this->cdnService->isEnabled()) {
            $this->error('Le CDN n\'est pas activé.');
            return 1;
        }
        
        $force = $this->option('force');
        $sourceDisk = Storage::disk('public_images');
        $targetDisk = $this->cdnService->getDisk();
        
        $this->info('Synchronisation des fichiers vers le CDN...');
        
        // Récupérer tous les fichiers
        $files = $sourceDisk->allFiles();
        $bar = $this->output->createProgressBar(count($files));
        
        $copied = 0;
        $skipped = 0;
        
        foreach ($files as $file) {
            // Vérifier si le fichier existe déjà
            if (!$force && $targetDisk->exists($file)) {
                $skipped++;
                continue;
            }
            
            // Copier le fichier
            $contents = $sourceDisk->get($file);
            $targetDisk->put($file, $contents);
            $copied++;
            
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine(2);
        
        $this->info("Synchronisation terminée: {$copied} fichiers copiés, {$skipped} ignorés.");
        
        return 0;
    }
}
```

Ajoutez cette commande à votre planificateur pour une synchronisation automatique :

```php
// app/Console/Kernel.php
protected function schedule(Schedule $schedule)
{
    // Synchroniser les fichiers vers le CDN tous les jours à 2h du matin
    $schedule->command('cdn:sync')->dailyAt('02:00');
}
```

## Optimisations de performance

### 1. Activer HTTP/2

Pour Apache (dans le VirtualHost HTTPS) :
```apache
Protocols h2 http/1.1
```

Pour Nginx :
```nginx
listen 443 ssl http2;
```

### 2. Configurer le Brotli

Pour Nginx (après installation du module) :
```nginx
brotli on;
brotli_types image/svg+xml;
```

### 3. Ajouter des en-têtes de sécurité

```nginx
add_header X-Content-Type-Options "nosniff";
add_header X-Frame-Options "SAMEORIGIN";
add_header X-XSS-Protection "1; mode=block";
```

### 4. Optimiser les images à la volée

Utilisez un service comme Thumbor ou Imgproxy pour redimensionner et optimiser les images à la volée :

```bash
# Installation d'Imgproxy
docker run -p 8080:8080 \
    -e IMGPROXY_ENABLE_WEBP_DETECTION=true \
    -e IMGPROXY_QUALITY=80 \
    darthsim/imgproxy
```

Puis modifiez votre service CDN pour générer des URLs vers Imgproxy :

```php
public function url(string $path, array $options = []): string
{
    $baseUrl = $this->getDisk()->url($path);
    
    if (empty($options)) {
        return $baseUrl;
    }
    
    // Construire l'URL Imgproxy
    $width = $options['width'] ?? null;
    $height = $options['height'] ?? null;
    
    if ($width || $height) {
        return "https://imgproxy.Lorrelei.com/resize:fit:{$width}:{$height}/plain/{$baseUrl}";
    }
    
    return $baseUrl;
}
```

## Conclusion

La configuration d'un dossier ou serveur séparé pour votre CDN offre une meilleure séparation des préoccupations et une plus grande flexibilité. Vous pouvez commencer avec un dossier dédié sur le même serveur, puis évoluer vers un serveur séparé ou un service CDN tiers à mesure que votre application se développe.

Pour une application en production avec un trafic important, l'utilisation d'un service CDN tiers comme Cloudflare ou AWS CloudFront est recommandée pour une performance optimale et une meilleure répartition géographique.
