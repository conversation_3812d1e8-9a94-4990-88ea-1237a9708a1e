<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Adresse extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'rue',
        'ville',
        'etat',
        'pays',
        'codePostal',
        'type',
        'nom',
        'prenom',
        'telephone',
        'est_defaut',
        'user_id',
        'zone_livraison_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'rue' => 'string',
        'ville' => 'string',
        'etat' => 'string',
        'pays' => 'string',
        'codePostal' => 'string',
        'type' => 'string',
        'nom' => 'string',
        'prenom' => 'string',
        'telephone' => 'string',
        'est_defaut' => 'boolean',
        'user_id' => 'integer',
        'zone_livraison_id' => 'integer',
    ];

    /**
     * Mutateur pour l'attribut rue
     */
    public function setRueAttribute($value)
    {
        $this->attributes['rue'] = $value === null ? '' : (string) $value;
    }

    /**
     * Mutateur pour l'attribut ville
     */
    public function setVilleAttribute($value)
    {
        $this->attributes['ville'] = $value === null ? '' : (string) $value;
    }

    /**
     * Mutateur pour l'attribut etat
     */
    public function setEtatAttribute($value)
    {
        $this->attributes['etat'] = $value === null ? '' : (string) $value;
    }

    /**
     * Mutateur pour l'attribut pays
     */
    public function setPaysAttribute($value)
    {
        $this->attributes['pays'] = $value === null ? '' : (string) $value;
    }

    /**
     * Mutateur pour l'attribut codePostal
     */
    public function setCodePostalAttribute($value)
    {
        $this->attributes['codePostal'] = $value === null ? '' : (string) $value;
    }

    /**
     * Mutateur pour l'attribut type
     */
    public function setTypeAttribute($value)
    {
        $this->attributes['type'] = $value === null ? '' : (string) $value;
    }

    /**
     * Mutateur pour l'attribut user_id
     */
    public function setUserIdAttribute($value)
    {
        $this->attributes['user_id'] = $value === null ? null : (int) $value;
    }

    public function clients(): HasMany
    {
        return $this->hasMany(Client::class);
    }

    public function marchands(): HasMany
    {
        return $this->hasMany(Marchand::class);
    }

    public function commandes(): HasMany
    {
        return $this->hasMany(Commande::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
