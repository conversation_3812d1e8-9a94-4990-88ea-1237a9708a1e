<?php

namespace App\Filament\Resources\CategorieResource\Pages;

use App\Filament\Resources\CategorieResource;
use App\Filament\Traits\HandlesImageStorage;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCategorie extends CreateRecord
{
    use HandlesImageStorage;

    protected static string $resource = CategorieResource::class;

    protected function afterCreate(): void
    {
        // Déplacer les images du dossier temporaire vers le dossier basé sur l'ID
        self::moveImagesAfterCreate($this->record, 'categories', 'image_url');
    }
}
