<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Commande extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'client_id',
        'marchand_id',
        'montantTotal',
        'statut',
        'adresse_livraison_id',
        'creeLe',
        'dateExpeditionPrevue',
        'dateLivraisonPrevue',
        'codeSuivi',
        'adresse_id',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'montantTotal' => 'decimal:2',
            'creeLe' => 'timestamp',
            'dateExpeditionPrevue' => 'date',
            'dateLivraisonPrevue' => 'date',
            'adresse_id' => 'integer',
        ];
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function marchand(): BelongsTo
    {
        return $this->belongsTo(Marchand::class);
    }

    public function adresse(): BelongsTo
    {
        return $this->belongsTo(Adresse::class);
    }

    public function adresseLivraison(): BelongsTo
    {
        return $this->belongsTo(Adress::class);
    }

    public function articleCommandes(): HasMany
    {
        return $this->hasMany(ArticleCommande::class);
    }
}
